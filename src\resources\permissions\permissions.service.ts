import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Permission } from './entities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import type { DataParams, DataResponse } from 'src/types/params';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async create(createPermissionDto: CreatePermissionDto) {
    if (typeof createPermissionDto.status === 'string') {
      createPermissionDto.status = createPermissionDto.status === 'true';
    }
    if (typeof createPermissionDto.isDefault === 'string') {
      createPermissionDto.isDefault = createPermissionDto.isDefault === 'true';
    }
    const permission = this.permissionRepository.create(createPermissionDto);
    return this.permissionRepository.save(permission);
  }

  async findAll(pag: DataParams): Promise<DataResponse<Permission>> {
    const [roles, total] = await this.permissionRepository.findAndCount({
      where: {
        name: pag.search ? Like(`%${pag.search}%`) : undefined,
      },
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
      cache: true, // Optional: Enable caching for performance
    });
    return {
      data: roles,
      total,
      curPage: pag.page,
      hasNext: total > pag.page * pag.limit,
      hasPrev: pag.page > 1,
    };
  }

  async findAllByStatus(perStatus: boolean) {
    return this.permissionRepository.find({ where: { status: perStatus } });
  }

  async findOne(id: number) {
    const permission = await this.permissionRepository.findOne({
      where: { id },
    });
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }
    return permission;
  }

  async setPermissionStatus(id: number, status: boolean) {
    const permission = await this.findOne(id);
    if (typeof status === 'string') {
      status = status === 'true';
    }
    permission.status = status;
    return this.permissionRepository.save(permission);
  }

  async update(id: number, updatePermissionDto: UpdatePermissionDto) {
    const permission = await this.findOne(id);
    if (typeof updatePermissionDto.status === 'string') {
      updatePermissionDto.status = updatePermissionDto.status === 'true';
    }
    if (typeof updatePermissionDto.isDefault === 'string') {
      updatePermissionDto.isDefault = updatePermissionDto.isDefault === 'true';
    }
    const updated = { ...permission, ...updatePermissionDto };
    return this.permissionRepository.save(updated);
  }

  async remove(id: number) {
    const permission = await this.findOne(id);
    return this.permissionRepository.remove(permission);
  }
}
