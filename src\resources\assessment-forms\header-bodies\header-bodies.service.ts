import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import type { UpdateHeaderBodyDto } from '../dto/updates/update-header-body.dto';
import { HeaderBody } from '../entities/header-body.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { AssessmentConfig } from 'src/configs/assessment.config';

@Injectable()
export class HeaderBodiesService {
  constructor(
    @InjectRepository(HeaderBody)
    private readonly headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
  ) {}

  async createHeaderBody(blockId: number) {
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: blockId },
    });

    if (!itemBlock) {
      throw new Error(`ItemBlock with ID ${blockId} not found`);
    }

    const headerBody = this.headerBodyRepository.create({
      title: AssessmentConfig.DEFAULT_HEADER_TITLE,
      description: AssessmentConfig.DEFAULT_HEADER_DESCRIPTION,
      itemBlock,
    });

    return await this.headerBodyRepository.save(headerBody);
  }

  //อย่าหายไปไหนอีกเลย สำคัญต่อการ dupicate
  async create(data: {
    itemBlockId: number;
    title: string;
    description?: string;
  }) {
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: data.itemBlockId },
    });

    if (!itemBlock) {
      throw new Error(`ItemBlock with ID ${data.itemBlockId} not found`);
    }

    const headerBody = this.headerBodyRepository.create({
      title: data.title,
      description: data.description || null,
      itemBlock,
    });

    return await this.headerBodyRepository.save(headerBody);
  }

  findAll(): Promise<HeaderBody[]> {
    return this.headerBodyRepository.find();
  }

  async findOne(id: number): Promise<HeaderBody> {
    const headerBody = await this.headerBodyRepository.findOneBy({ id });
    if (!headerBody) {
      throw new NotFoundException(`HeaderBody with ID ${id} not found`);
    }
    return headerBody;
  }

  async update(id: number, updateHeaderBodyDto: UpdateHeaderBodyDto) {
    const { itemBlockId, title, description, ...rest } = updateHeaderBodyDto;
    const headerBody = await this.headerBodyRepository.findOne({
      where: { id },
      relations: ['itemBlock'],
    });

    if (!headerBody) {
      throw new Error(`HeaderBody with ID ${id} not found`);
    }
    if (itemBlockId) {
      const itemBlock = await this.itemBlockRepository.findOne({
        where: { id: itemBlockId },
      });

      if (!itemBlock) {
        throw new Error(`ItemBlock with ID ${itemBlockId} not found`);
      }

      headerBody.itemBlock = itemBlock;
    }
    headerBody.title = title ?? headerBody.title;
    headerBody.description =
      description === '' ? null : (description ?? headerBody.description);

    Object.assign(headerBody, rest);
    const updated = await this.headerBodyRepository.save(headerBody);

    return {
      id: updated.id,
      title: updated.title,
      description: updated.description,
      itemBlockId: updated.itemBlock.id,
      ...rest,
    };
  }

  remove(id: number) {
    return  this.headerBodyRepository
      .findOne({ where: { id } })
      .then((question) => {
        if (!question) {
          throw new NotFoundException(`Question with ID ${id} not found`);
        }
        return this.headerBodyRepository.delete({ id });
      });
  }
  async deleteByItemBlockId(itemBlockId: number): Promise<void> {
    await this.headerBodyRepository.delete({ itemBlock: { id: itemBlockId } });
  }
}
