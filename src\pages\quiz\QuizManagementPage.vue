<template>
  <q-page padding>
    <!-- หัวข้อ -->
    <div class="text-h6 q-mb-md">จัดการแบบทดสอบ</div>

    <!-- แถบบน: Search + เพิ่ม -->
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar @search="onSearchUpdate" />
      <q-btn label="สร้าง" color="accent" icon="add" @click="onClickCreate"> </q-btn>
    </div>

    <q-table
      :rows="rows"
      :columns="quizManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="Loading.isActive"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <template v-slot:body-cell-link="{ row }">
        <q-td class="text-center">
          <q-btn flat dense icon="link" :disable="!row.assessmentLink" aria-label="Open link" />
        </q-td>
      </template>

      <!-- Actions Column (เหมือนเดิม) -->
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              @click="onClickPreview(row)"
            />
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn
              dense
              unelevated
              class="edit-graph-icon"
              icon="bar_chart"
              @click="onClickChart(row)"
            />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>

    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar, Loading } from 'quasar';
import { quizManagementColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import { api } from 'src/boot/axios';
import SearchBar from 'src/components/SearchBar.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useGlobalStore } from 'src/stores/global';
import { useQuizStore } from 'src/stores/quiz';
import type { ItemBlock } from 'src/types/models';

const globalStore = useGlobalStore();
const router = useRouter();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');

// async function onClickCreate() {
//   const user = useAuthStore().getCurrentUser();
//   const res = await new AssessmentService('quiz').createOne({
//     creatorUserId: user?.id || 1,
//     programId: 1,
//     type: 'QUIZ',
//   });
//   await router.push({
//     name: 'quiz-edit',
//     params: { id: res.id },
//     hash: '#questions',
//   });
// }

async function onClickCreate() {
  try {
    console.log('onClickCreate');

    const user = useAuthStore().getCurrentUser();
    const quizStore = useQuizStore();

    // Step 1: Create Assessment
    const assessmentResponse = await new AssessmentService('quiz').createOne({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'QUIZ',
    });

    // Step 2: Try to Create Header Block (Expected to fail due to backend bug)
    let headerCreated = false;
    try {
      await api.post<ItemBlock>('/item-blocks/block', {
        assessmentId: assessmentResponse.id,
        type: 'HEADER',
        sequence: 1,
      });
      headerCreated = true;
    } catch {
      // Header creation failed - expected due to backend bug
    }

    // Step 3: Create Radio ItemBlock (Should work)
    await api.post<ItemBlock>('/item-blocks/block', {
      assessmentId: assessmentResponse.id,
      type: 'RADIO',
      sequence: headerCreated ? 2 : 1,
    });

    // Store the created assessment immediately in the evaluate form store
    quizStore.currentAssessment = assessmentResponse;

    // CRITICAL: Fetch the complete assessment data including itemBlocks before navigation
    try {
      await quizStore.fetchAssessmentById(assessmentResponse.id);

      // Validate that we have itemBlocks before proceeding
      if (
        !quizStore.currentAssessment?.itemBlocks ||
        quizStore.currentAssessment.itemBlocks.length === 0
      ) {
        // No itemBlocks found - BlockCreator will handle this
      }
    } catch {
      // Failed to fetch complete assessment data
    }

    await router.push({
      name: 'evaluate-edit',
      query: { mode: 'edit' },
      params: { id: assessmentResponse.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
  }
}

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService('quiz').fetchAll(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService('quiz').fetchAll(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    await router.push({
      name: 'quiz-preview',
      params: { linkUrl: row.linkURL.toString() }, // ✅ ใช้ linkUrl param
    });
  } catch (error) {
    console.error('Navigation to preview failed:', error);
  }
}

async function onClickChart(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    await router.push({
      name: 'quiz-edit',
      params: { id: row.id.toString() },
      hash: 'replies',
    });
  } catch (error) {
    console.error('Navigation to graph view failed:', error);
    notify({ type: 'negative', message: 'ไม่สามารถไปยังหน้ากราฟได้' });
  }
}

async function onClickEdit(row: Assessment) {
  globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
  const resolvedRoute = router.resolve({
    name: 'quiz-edit',
    params: { id: row.id.toString() },
    hash: 'questions',
  });
  await router.push(resolvedRoute);
}

function onClickDelete(row: Assessment) {
  selectedRowToDelete.value = row;
  titleDialog.value = `ยืนยันการลบแบบทดสอบ ${row.name}`;
  confirmDialogVisible.value = true;
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('quiz').deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
});
</script>
<style scoped lang="scss">
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: black !important;
}

.view-icon {
  background-color: #39303d;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
