import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseInterceptors,
  UseGuards,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiConsumes,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { CreateAssessmentDto } from './dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/updates/update-assessment.dto';
import { Assessment } from './entities/assessment.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from './enums/assessment-type.enum';
import { AssessmentsService } from './services/assessments.service';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { QuizDashboardService } from './services/quiz.dashboard.service';
import { EvaluateDashBoardService } from './services/evaluate.dashboard.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { Permission } from 'src/auth/permission.decorator';
import { PermissionGuard } from 'src/auth/permission.guard';
import { Request } from 'express';
import { QuizService } from './services/quiz.service';
import { MockupDataService } from './services/mockup-data.service';
import { Submission } from './entities/submission.entity';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';

@ApiTags('Assessment')
@Controller('assessments')
export class AssessmentsController {
  constructor(
    private readonly assessmentsService: AssessmentsService,
    private readonly quizDashboardService: QuizDashboardService,
    private readonly evaluateDashboardService: EvaluateDashBoardService,
    private readonly quizService: QuizService,
    private readonly mockupDataService: MockupDataService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Permission('create_form')
  @Post()
  @ApiOperation({
    summary: 'New Assessment (Quiz/Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new assessment with the required fields.',
    schema: {
      type: 'object',
      properties: {
        creatorUserId: { type: 'number', example: 1 },
        type: {
          type: 'string',
          enum: [AssessmentType.QUIZ, AssessmentType.EVALUATE],
          example: AssessmentType.QUIZ,
        },
        programId: { type: 'number', example: 1 },
      },
      required: ['programId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() dto: CreateAssessmentDto) {
    console.log('Create Assessment DTO:', dto);
    return this.assessmentsService.createOne(dto);
  }
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Permission('view_all_forms')
  @Get()
  @DefaultQueryParams()
  @ApiQuery({
    name: 'type',
    required: false,
    enum: AssessmentType,
  })
  async getAll(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAll(query, type);
  }

  @Get('prototypes')
  @ApiOperation({
    summary: 'Get all assessments that are prototypes (isPrototype=true)',
  })
  @DefaultQueryParams()
  @ApiQuery({
    name: 'type',
    required: true,
    enum: AssessmentType,
  })
  async getAllPrototypes(
    @Query('type') type: AssessmentType,
    @Query() query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAllPrototypes(type, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single assessment by ID' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the assessment',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved assessment.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Assessment> {
    return this.assessmentsService.findOne(id);
  }

  @Get('/preview/:id/:section')
  getOne(@Param('id') id: number, @Param('section') section: number) {
    return this.assessmentsService.findOneBySection(+id, +section);
  }
  // This is Update of Assessment
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Permission('edit_own_form')
  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing assessment' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Update an existing assessment with the required fields.',
    required: true,
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Updated Assessment Name' },
        status: { type: 'boolean', example: false },
        startAt: {
          type: 'string',
          format: 'date-time',
          example: new Date(),
        },
        endAt: {
          type: 'string',
          format: 'date-time',
          example: (() => {
            const date = new Date();
            date.setDate(date.getDate() + 1); // Add 1 day
            return date;
          })(),
        },
        submitLimit: { type: 'number', example: 1 },
        linkURL: { type: 'string', example: 'https://example.com/assessment' },
        timeout: {
          type: 'number',
          example: 1000,
          description: 'Allowed Second',
        }, // input Second
        isPrototype: { type: 'boolean', example: false },
        passRatio: {
          type: 'number',
          format: 'float',
          example: 0.5,
          description: 'Allowed Decimal ex.0.5 , 1.5',
        },
        totalScore: {
          type: 'number',
          example: 10,
          description: 'In currently If Evaluate Assessment set to Null',
        },
        responseEdit: { type: 'boolean', example: false },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The assessment has been successfully updated.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @UseInterceptors(AnyFilesInterceptor())
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    return this.assessmentsService.update(id, updateAssessmentDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }

  // * Dashboard endpoints for Quiz
  @Get('dashboard/quiz/:id')
  @ApiOperation({ summary: 'ดึงข้อมูลแดชบอร์ดของแบบทดสอบ' })
  @ApiParam({
    name: 'id',
    description: 'รหัสของแบบทดสอบ',
    type: Number,
  })
  async getQuizDashboardMeta(@Param('id', ParseIntPipe) id: number) {
    return this.quizDashboardService.generateAssessmentMeta(id);
  }

  @Get('dashboard/quiz/:id/score-distribution')
  @ApiOperation({ summary: 'ดึงข้อมูลกราฟการกระจายคะแนนของแบบทดสอบ' })
  @ApiParam({
    name: 'id',
    description: 'รหัสของแบบทดสอบ',
    type: Number,
  })
  async getQuizScoreDistribution(@Param('id', ParseIntPipe) id: number) {
    return this.quizDashboardService.generateScoreDistributionChart(id);
  }

  @Get('dashboard/quiz/:id/questions')
  @ApiOperation({ summary: 'ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ' })
  @ApiParam({
    name: 'id',
    description: 'รหัสของแบบทดสอบ',
    type: Number,
  })
  async getQuizQuestionResponses(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.generateAllQuestionResponses(id, query);
  }

  @Get('dashboard/quiz/:id/participants')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด' })
  @ApiParam({
    name: 'id',
    description: 'รหัสของแบบทดสอบ',
    type: Number,
  })
  async getQuizParticipants(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.getAllParticipants(id, query);
  }

  @Get('dashboard/quiz/participant/:id')
  @ApiOperation({
    summary: 'ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย',
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสของผู้เข้าร่วม',
    type: Number,
  })
  async getQuizParticipantDetails(@Param('id', ParseIntPipe) id: number) {
    return this.quizDashboardService.getOneParticipant(id);
  }

  // // * Example Dashboard for Evaluate
  // @Get('dashboard/evaluate')
  // async getEvaluateDashboard() {
  //   // return this.evaluateService.getDashboard();
  // }

  // แบบประเมินสำหรับ user
  @Get('response/evaluate')
  async getAllEvaluate(@Query() query: DataParams) {
    // return this.evaluateService.getResponseAll(userId);
  }

  @Get('dashboard/evaluate/:assessmentId')
  @ApiOperation({ summary: 'ดึงข่อมูลสำหรับโชว์ Graph' })
  async getChartData(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
  ) {
    return this.evaluateDashboardService.getChartData(assessmentId);
  }

  @ApiOperation({ summary: 'ดึงข่อมูลจำนวนของResponses ทั้งหมด' })
  @Get('/header/:assessmentId')
  async getNumberOfResponses(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
  ): Promise<number> {
    return this.evaluateDashboardService.getNumberOfResponses(assessmentId);
  }
  @Patch(':sourceId/copy-to/:targetId')
  @ApiOperation({ summary: 'Copy content from one assessment to another' })
  @ApiParam({
    name: 'sourceId',
    description: 'Source assessment ID',
    type: Number,
  })
  @ApiParam({
    name: 'targetId',
    description: 'Target assessment ID',
    type: Number,
  })
  async copyAssessmentContent(
    @Param('sourceId', ParseIntPipe) sourceId: number,
    @Param('targetId', ParseIntPipe) targetId: number,
  ): Promise<Assessment> {
    return this.assessmentsService.duplicateAssessment(sourceId, targetId);
  }

  // Test Auth Guard
  // @ApiBearerAuth()
  // @UseGuards(AuthGuard)
  // @Permission('view_all_forms')
  // @ApiOperation({ summary: 'Test Permission' })
  // @Get('test-permission/view')
  // async getAllQuiz() {
  //   return 'Test Route Permission';
  // }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Permission('view_own_forms')
  @Get('editor/view-assessment')
  @DefaultQueryParams()
  @ApiQuery({
    name: 'type',
    required: false,
    enum: AssessmentType,
  })
  async getAllEditor(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
    @Req() request: Request,
  ): Promise<DataResponse<Assessment>> {
    const user = request['user']; // Access user from the request
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return this.assessmentsService.getAllEditor(query, type, user); // Pass user to service
  }

  //Path for Show all Assessment of StandardUser
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('standardUser/view-assessment')
  @DefaultQueryParams()
  @ApiQuery({
    name: 'type',
    required: false,
    enum: AssessmentType,
  })
  async getAllUser(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAllUser(query, type);
  }

  @Get('url/:url')
  async getByURL(@Param('url') uuid: string) {
    return this.assessmentsService.getByURL(uuid);
  }

  @Post('mockup')
  @ApiOperation({
    summary: 'Create Mockup Assessment Data',
    description:
      'Creates a complete assessment with 100 questions (25 questions each for RADIO, CHECKBOX, TEXTFIELD, and UPLOAD types)',
  })
  async createMockupAssessment(): Promise<Assessment> {
    return this.mockupDataService.createMockupQuizAssessment();
  }

  @Post('mockup/submissions/:id')
  @ApiOperation({
    summary: 'Create Mockup Submissions for Assessment',
    description:
      'Creates 50 mockup submissions with realistic response patterns for the specified assessment',
  })
  @ApiParam({
    name: 'id',
    description: 'Assessment ID to create submissions for',
    type: Number,
  })
  async createMockupSubmissions(
    @Param('id', ParseIntPipe) assessmentId: number,
  ): Promise<Submission[]> {
    return this.mockupDataService.createMockupSubmissions(assessmentId, 50);
  }

  @Post('mockup/complete')
  @ApiOperation({
    summary: 'Create Complete Mockup Data (Assessment + Submissions)',
    description:
      'Creates a complete assessment with 100 questions and 50 submissions for dashboard testing',
  })
  async createCompleteAssessmentMockup(): Promise<{
    assessment: Assessment;
    submissions: Submission[];
  }> {
    return this.mockupDataService.createCompleteAssessmentWithSubmissions(50);
  }
  @Get('header-with-submissions/url/:linkUrl')
  @ApiOperation({ summary: 'Get quiz header with user submission history' })
  async getQuizHeaderWithUserSubmissions(
    @Param('linkUrl') linkUrl: string,
    @Query('userId') userId?: number,
  ) {
    return this.quizService.getQuizHeaderWithUserSubmissions(linkUrl, userId);
  }
}
