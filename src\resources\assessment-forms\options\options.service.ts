import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Option } from '../entities/option.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { Question } from '../entities/question.entity';
import type { CreateOptionDto } from '../dto/creates/create-option.dto';
import type { UpdateOptionDto } from '../dto/updates/update-option.dto';
import { FileUploadService } from '../utils/file-upload.service';

@Injectable()
export class OptionsService {
  constructor(
    @InjectRepository(Option)
    private readonly optionRepository: Repository<Option>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async getMaxSequence(itemBlockId: number): Promise<number> {
    const lastQuestion = await this.optionRepository
      .createQueryBuilder('option')
      .where('option.itemBlockId = :itemBlockId', { itemBlockId })
      .orderBy('option.sequence', 'DESC')
      .getOne();

    return lastQuestion ? lastQuestion.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async create(
    createOptionDto: CreateOptionDto,
    file?: Express.Multer.File,
  ): Promise<Option> {
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: createOptionDto.itemBlockId },
    });
    if (!itemBlock)
      throw new Error(
        `ItemBlock with id ${createOptionDto.itemBlockId} not found`,
      );

    let imagePath = createOptionDto.imagePath?.trim() || null;
    if (file) {
      imagePath = await this.fileUploadService.uploadAndGetImagePath(file);
    }

    const option = this.optionRepository.create({
      ...createOptionDto,
      optionText: createOptionDto.optionText?.trim() || '',
      imagePath,
      itemBlock,
      sequence: await this.getMaxSequence(createOptionDto.itemBlockId),
    });

    return await this.optionRepository.save(option);
  }

  async findAll(): Promise<Option[]> {
    return this.optionRepository.find({
      relations: ['itemBlock', 'responses'],
    });
  }

  async findOne(id: number): Promise<Option> {
    return this.optionRepository.findOne({
      where: { id },
      relations: ['itemBlock', 'responses'],
    });
  }

  async update(
    id: number,
    updateDto: UpdateOptionDto,
    file?: Express.Multer.File,
  ): Promise<Option> {
    const existingOption = await this.optionRepository.findOne({
      where: { id },
    });
    if (!existingOption) throw new Error(`Option with id ${id} not found`);

    let newImagePath = updateDto.imagePath?.trim() || null;

    if (file) {
      newImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
      if (existingOption.imagePath) {
        await this.fileUploadService.deleteFileByUrl(existingOption.imagePath);
      }
    } else if (newImagePath === null && existingOption.imagePath) {
      await this.fileUploadService.deleteFileByUrl(existingOption.imagePath);
    }

    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: updateDto.itemBlockId },
    });
    if (!itemBlock)
      throw new Error(`ItemBlock with id ${updateDto.itemBlockId} not found`);
    await this.optionRepository.update(id, {
      ...updateDto,
      optionText: updateDto.optionText?.trim() || '',
      imagePath: newImagePath,
      itemBlock,
    });

    return this.optionRepository.findOne({
      where: { id },
      relations: ['itemBlock', 'responses'],
    });
  }

  async remove(id: number): Promise<void> {
    const option = await this.optionRepository.findOne({ where: { id },});
    if (!option) {
      throw new Error(`Option with id ${id} not found`);
    }
    if (option.imagePath) {
      await this.fileUploadService.deleteFileByUrl(option.imagePath);
    }
    await this.optionRepository.delete(id);
  }

  async removeByItemBlockId(itemBlockId: number): Promise<void> {
    await this.optionRepository.delete({ itemBlockId });
  }

  // New methods for question-based operations
  async createForQuestion(
    questionId: number,
    createOptionDto: CreateOptionDto,
    file?: Express.Multer.File,
  ): Promise<Option> {
    // Validate that the question exists
    const question = await this.questionRepository.findOne({
      where: { id: questionId },
    });

    if (!question) {
      throw new Error(`Question with id ${questionId} not found`);
    }

    // Use the itemBlockId from the request body (as required)
    const itemBlockId = createOptionDto.itemBlockId;
    if (!itemBlockId) {
      throw new Error('itemBlockId is required in request body');
    }

    // Validate that the itemBlock exists
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: itemBlockId },
    });

    if (!itemBlock) {
      throw new Error(`ItemBlock with id ${itemBlockId} not found`);
    }

    // Validate that the question belongs to the specified itemBlock
    if (question.itemBlockId !== itemBlockId) {
      throw new Error(
        `Question ${questionId} does not belong to ItemBlock ${itemBlockId}`,
      );
    }

    let imagePath = createOptionDto.imagePath?.trim() || null;
    if (file) {
      imagePath = await this.fileUploadService.uploadAndGetImagePath(file);
    }

    const option = this.optionRepository.create({
      ...createOptionDto,
      optionText: createOptionDto.optionText?.trim() || '',
      imagePath,
      itemBlock,
      itemBlockId: itemBlockId,
      sequence: await this.getMaxSequence(itemBlockId),
    });

    return await this.optionRepository.save(option);
  }

  async updateForQuestion(
    questionId: number,
    optionId: number,
    updateDto: UpdateOptionDto,
    file?: Express.Multer.File,
  ): Promise<Option> {
    // Validate that the question exists
    const question = await this.questionRepository.findOne({
      where: { id: questionId },
    });

    if (!question) {
      throw new Error(`Question with id ${questionId} not found`);
    }

    // Use the itemBlockId from the request body (as required)
    const itemBlockId = updateDto.itemBlockId;
    if (!itemBlockId) {
      throw new Error('itemBlockId is required in request body');
    }

    // Validate that the question belongs to the specified itemBlock
    if (question.itemBlockId !== itemBlockId) {
      throw new Error(
        `Question ${questionId} does not belong to ItemBlock ${itemBlockId}`,
      );
    }

    // Find the existing option and verify it belongs to the correct itemBlock
    const existingOption = await this.optionRepository.findOne({
      where: { id: optionId, itemBlockId: itemBlockId },
    });

    if (!existingOption) {
      throw new Error(
        `Option with id ${optionId} not found for ItemBlock ${itemBlockId}`,
      );
    }

    let newImagePath = updateDto.imagePath?.trim() || null;

    if (file) {
      newImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
      if (existingOption.imagePath) {
        await this.fileUploadService.deleteFileByUrl(existingOption.imagePath);
      }
    } else if (newImagePath === null && existingOption.imagePath) {
      await this.fileUploadService.deleteFileByUrl(existingOption.imagePath);
    }

    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: itemBlockId },
    });

    if (!itemBlock) {
      throw new Error(`ItemBlock with id ${itemBlockId} not found`);
    }

    await this.optionRepository.update(optionId, {
      ...updateDto,
      optionText: updateDto.optionText?.trim() || '',
      imagePath: newImagePath,
      itemBlock,
      itemBlockId: itemBlockId,
    });

    return this.optionRepository.findOne({
      where: { id: optionId },
      relations: ['itemBlock', 'responses'],
    });
  }
}
