import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import type { DataParams, DataResponse } from 'src/types/params';
import { Submission } from '../entities/submission.entity';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import { ChartData } from '../dto/chart-data.dto';
import { ApiService } from 'src/api/api.service';

@Injectable()
export class EvaluateDashBoardService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
    private apiService: ApiService,
  ) {}

async getChartData(assessmentId: number): Promise<ChartData[]> {
  const chartDatas: ChartData[] = [];

  interface FileData {
    files: { fileName: string; downloadName?: string }[];
  }

  const assessment = await this.assessmentRepo
    .createQueryBuilder('a')
    .leftJoinAndSelect('a.itemBlocks', 'ib')
    .leftJoinAndSelect('ib.headerBody', 'h')
    .leftJoinAndSelect('ib.questions', 'q')
    .leftJoinAndSelect('q.responses', 'r')
    .leftJoinAndSelect('r.submission', 's')
    .leftJoinAndSelect('s.user', 'u')
    .leftJoinAndSelect('r.selectedOption', 'so')
    .leftJoinAndSelect('ib.options', 'o')
    .where('a.id = :id', { id: assessmentId })
    .andWhere('ib.type NOT IN (:...types)', {
      types: ['IMAGE'],
    })
    .orderBy('q.sequence', 'ASC')
    .addOrderBy('o.sequence', 'ASC')
    .getOne();

  if (!assessment) {
    throw new NotFoundException(`Assessment with ID ${assessmentId} not found`);
  }

  const maxSection = Math.max(...assessment.itemBlocks.map((ib) => ib.section));
  const processedHeaderSections = new Set<number>();

  for (const itemBlock of assessment.itemBlocks) {
    const sectionLabel = `${itemBlock.section},${maxSection}`;

    if (
      itemBlock.type === 'HEADER' &&
      !processedHeaderSections.has(itemBlock.section) &&
      itemBlock.section !== 1
    ) {
      chartDatas.push({
        section: sectionLabel,
        sequence: itemBlock.sequence || 1,
        title: itemBlock.headerBody?.title || `หัวข้อที่ ${itemBlock.section}`,
        labels: [],
        datasets: [{ label: '', values: [] }],
        type: 'header',
      });
      processedHeaderSections.add(itemBlock.section);
    }

    let type: string;
    if (itemBlock.type === 'UPLOAD') {
      type = 'image';
    } else if (['RADIO', 'CHECKBOX', 'GRID'].includes(itemBlock.type)) {
      type = 'choice';
    } else {
      type = 'text';
    }

    let title = '';
    if (type === 'text') {
      const textQuestions = itemBlock.questions.filter((q) => q.isHeader === false);
      title = textQuestions.length > 0 ? textQuestions[0].questionText : '';
    } else {
      const titleQuestion = itemBlock.questions.find((q) => q.isHeader === true);
      title = titleQuestion ? titleQuestion.questionText : '';
    }

    if (type === 'image') {
      const questions = itemBlock.questions.filter((q) => q.isHeader === false);
      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const files: FileData['files'] = options.map((option) => ({
        fileName: option.imagePath || option.optionText || 'ตัวเลือก....',
      }));

      let viewUrls: string[] = options.map(
        (option) => option.imagePath || option.optionText || 'ตัวเลือก....',
      );

      try {
        const response = await this.apiService.getPublicFiles(files);
        if (response?.status === 'success' && response.result) {
          viewUrls = options.map((_, index) => {
            const fileKey = `file_${index + 1}`;
            return response.result[fileKey]?.view || viewUrls[index];
          });
        }
      } catch (error) {
        console.error('ApiService Error:', error.message);
      }

      const responses = questions
        .flatMap((q) => q.responses || [])
        .filter((r) => r.submission?.user?.name && r.selectedOption)
        .sort((a, b) => {
          const optionA = options.find((o) => o.id === a.selectedOption.id);
          const optionB = options.find((o) => o.id === b.selectedOption.id);
          return (optionA?.sequence || 0) - (optionB?.sequence || 0);
        });

      const userNames = responses.map((r) => r.submission.user.name);
      const datasets = options.map((option, index) => ({
        label: viewUrls[index],
        values: [],
      }));

      if (datasets.length > 0) {
        chartDatas.push({
          section: sectionLabel,
          sequence: itemBlock.sequence,
          title: questions.length > 0 ? questions[0].questionText : title,
          labels: userNames,
          datasets,
          type: 'image',
        });
      }
    } else if (type === 'choice') {
      const questions = itemBlock.questions
        .filter((q) => q.isHeader === false)
        .sort((a, b) => a.sequence - b.sequence);

      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const labels = questions.map((q) => q.questionText);
      const datasets = options.map((option) => {
        const values = questions.map((question) => {
          const responseCount = question.responses
            ? question.responses.filter(
                (r) => r.selectedOption && r.selectedOption.id === option.id,
              ).length
            : 0;
          return responseCount;
        });
        return { label: option.optionText, values };
      });

      chartDatas.push({
        section: sectionLabel,
        sequence: itemBlock.sequence,
        title,
        labels,
        datasets,
        type,
      });
    } else {
      // type === 'text'
      const questions = itemBlock.questions.filter((q) => q.isHeader === false);
      const allResponses = questions.flatMap((q) => q.responses || []);
      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const datasets = options.map((option) => {
        const count = allResponses.filter(
          (r) => r.selectedOption && r.selectedOption.id === option.id,
        ).length;
        return {
          label: option.optionText,
          values: [count],
        };
      });

      const allLabelsAreNumbers = datasets.every((d) => !isNaN(Number(d.label)));

      if (datasets.length > 0) {
        if (allLabelsAreNumbers) {
          // เปลี่ยน labels และ type ตามคำขอ
          chartDatas.push({
            section: sectionLabel,
            sequence: itemBlock.sequence,
            title: '',         // title เป็น empty string
            labels: [title],   // title ย้ายไปอยู่ใน labels
            datasets,
            type: 'choice',    // เปลี่ยน type เป็น choice
          });
        } else {
          // แบบเดิม
          chartDatas.push({
            section: sectionLabel,
            sequence: itemBlock.sequence,
            title,
            labels: [],
            datasets,
            type,
          });
        }
      }
    }
  }

  return chartDatas.sort((a, b) => {
    const sectionA = parseInt(a.section.split(',')[0], 10);
    const sectionB = parseInt(b.section.split(',')[0], 10);
    if (sectionA !== sectionB) {
      return sectionA - sectionB;
    }
    return a.sequence - b.sequence;
  });
}


  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const result = await this.assessmentRepo.query(
      `SELECT A.name, COUNT(*) as number
       FROM assessments AS A
       JOIN submissions AS S ON A.id = S.assessmentId
       WHERE A.id = ${assessmentId}`,
    );
    return result[0];
  }
}