<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: number | string | undefined;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | string | undefined): void;
}>();

const text = ref<string | number | undefined>(
  typeof props.modelValue === 'number' ? props.modelValue.toString() : props.modelValue,
);

watch(
  () => props.modelValue,
  (newValue) => {
    text.value = typeof newValue === 'number' ? newValue.toString() : newValue;
  },
);

watch(text, (val) => {
  const parsedValue = val !== undefined && val !== '' ? Number(val) || val : undefined;
  emit('update:modelValue', parsedValue);
});
</script>

<template>
  <q-input v-model="text" placeholder="กรุณากรอกข้อมูล..." class="custom-input" outlined />
</template>

<style scoped>
.custom-input {
  width: 267px;
  height: 45px;
  border-radius: 12px;
  font-size: 16px;
}

/* ปรับข้อความให้อยู่กลางแนวตั้ง */
.custom-input :deep(input) {
  padding: 0 12px;
  height: 100%;
  display: flex;
}
</style>
