import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';
import { HeaderBody } from '../entities/header-body.entity';
import { ImageBody } from '../entities/image-body.entity';
import { Submission } from '../entities/submission.entity';
import { Response } from '../entities/response.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentType } from '../enums/assessment-type.enum';
import { ItemBlockType } from '../enums/item-block-type.enum';

@Injectable()
export class MockupDataService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(ItemBlock)
    private itemBlockRepository: Repository<ItemBlock>,
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Option)
    private optionRepository: Repository<Option>,
    @InjectRepository(HeaderBody)
    private headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ImageBody)
    private imageBodyRepository: Repository<ImageBody>,
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    @InjectRepository(Response)
    private responseRepository: Repository<Response>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async createMockupQuizAssessment(): Promise<Assessment> {
    const userId = 1; // ID ของผู้สร้างแบบทดสอบ (สามารถปรับตามต้องการ)

    // สร้าง Assessment หลัก
    const assessment = this.assessmentRepository.create({
      name: 'แบบทดสอบความรู้ทั่วไปแบบหลากหลาย 100 ข้อ',
      type: AssessmentType.QUIZ,
      status: true,
      startAt: new Date(),
      endAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 วันจากตอนนี้
      submitLimit: 3,
      timeout: 7200, // 2 ชั่วโมง
      totalScore: 420, // (40x3) + (30x4) + (15x10) + (15x5) = 120 + 120 + 150 + 75 = 465
      passRatio: 0.6,
      responseEdit: false,
      linkURL: `mockup-quiz-${Date.now()}`,
      creator: { id: userId } as any,
      program: { id: 1 } as any,
    });

    const savedAssessment = await this.assessmentRepository.save(assessment);

    // สร้างข้อมูลทั้งหมดแบบ batch เพื่อเพิ่มประสิทธิภาพ
    await this.createAllDataBatch(savedAssessment.id);

    return this.assessmentRepository.findOne({
      where: { id: savedAssessment.id },
      relations: [
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });
  }

  private async createAllDataBatch(assessmentId: number): Promise<void> {
    // กำหนดการกระจายคำถาม
    const questionDistribution = [
      { type: ItemBlockType.RADIO, count: 40 },
      { type: ItemBlockType.CHECKBOX, count: 30 },
      { type: ItemBlockType.TEXTFIELD, count: 15 },
      { type: ItemBlockType.IMAGE, count: 15 },
    ];

    // 🚀 Parallel Processing: สร้าง Header และ ItemBlocks พร้อมกัน
    const [savedItemBlocks] = await Promise.all([
      this.createItemBlocksBatch(assessmentId, questionDistribution),
      this.createHeaderBlock(assessmentId),
    ]);

    // 🚀 Pre-calculate ข้อมูลทั้งหมดแบบ  chunks
    const chunks = this.createDataChunks(savedItemBlocks, questionDistribution);

    // 🚀 Process chunks in parallel
    const results = await Promise.all(
      chunks.map((chunk) => this.processChunk(chunk)),
    );

    // 🚀 Flatten และ bulk save ทั้งหมดพร้อมกัน
    const { questions, options, imageBodies } = this.flattenResults(results);

    await Promise.all([
      questions.length > 0
        ? this.questionRepository.save(questions)
        : Promise.resolve(),
      options.length > 0
        ? this.optionRepository.save(options)
        : Promise.resolve(),
      imageBodies.length > 0
        ? this.imageBodyRepository.save(imageBodies)
        : Promise.resolve(),
    ]);
  }

  // 🚀 สร้าง ItemBlocks แบบ batch แยกออกมา
  private async createItemBlocksBatch(
    assessmentId: number,
    questionDistribution: Array<{ type: ItemBlockType; count: number }>,
  ): Promise<ItemBlock[]> {
    const itemBlocks = [];
    let sequence = 2; // เริ่มที่ 2 เพราะ header เป็น 1

    for (const { type, count } of questionDistribution) {
      for (let i = 0; i < count; i++) {
        itemBlocks.push(
          this.itemBlockRepository.create({
            sequence: sequence++,
            section: 1,
            type,
            isRequired: true,
            assessment: { id: assessmentId } as any,
          }),
        );
      }
    }

    return this.itemBlockRepository.save(itemBlocks);
  }

  // 🚀 แบ่งข้อมูลเป็น chunks เพื่อ process แบบ parallel
  private createDataChunks(
    savedItemBlocks: ItemBlock[],
    questionDistribution: Array<{ type: ItemBlockType; count: number }>,
  ) {
    const chunkSize = 25; // Process 25 items per chunk
    const chunks = [];

    let globalQuestionNumber = 1;
    let distributionIndex = 0;
    let countInType = 0;

    for (let i = 0; i < savedItemBlocks.length; i += chunkSize) {
      const chunk = {
        itemBlocks: savedItemBlocks.slice(i, i + chunkSize),
        startQuestionNumber: globalQuestionNumber,
        distributionMap: new Map<
          number,
          { type: ItemBlockType; questionNumber: number }
        >(),
      };

      // Map itemBlock index กับ type และ question number
      for (
        let j = 0;
        j < chunk.itemBlocks.length && i + j < savedItemBlocks.length;
        j++
      ) {
        const currentDistribution = questionDistribution[distributionIndex];
        chunk.distributionMap.set(j, {
          type: currentDistribution.type,
          questionNumber: globalQuestionNumber++,
        });

        countInType++;
        if (countInType >= currentDistribution.count) {
          distributionIndex++;
          countInType = 0;
        }
      }

      chunks.push(chunk);
    }

    return chunks;
  }

  // 🚀 Process แต่ละ chunk แบบ parallel
  private async processChunk(chunk: any) {
    const questions = [];
    const options = [];
    const imageBodies = [];

    // Process ข้อมูลใน chunk นี้
    chunk.itemBlocks.forEach((itemBlock: ItemBlock, index: number) => {
      const { type, questionNumber } = chunk.distributionMap.get(index);

      // สร้าง ImageBody สำหรับ IMAGE type
      if (type === ItemBlockType.IMAGE) {
        imageBodies.push(
          this.createImageBodyData(itemBlock.id, questionNumber),
        );
      }

      // สร้าง Question
      questions.push(
        this.createQuestionData(itemBlock.id, type, questionNumber),
      );

      // สร้าง Options สำหรับ RADIO, CHECKBOX และ IMAGE
      if (
        [ItemBlockType.RADIO, ItemBlockType.CHECKBOX].includes(type) ||
        (type === ItemBlockType.IMAGE && questionNumber % 3 !== 0)
      ) {
        options.push(
          ...this.createOptionsData(itemBlock.id, type, questionNumber),
        );
      }
    });

    return { questions, options, imageBodies };
  }

  // 🚀 รวม results จากทุก chunks
  private flattenResults(results: any[]) {
    return results.reduce(
      (acc, result) => ({
        questions: [...acc.questions, ...result.questions],
        options: [...acc.options, ...result.options],
        imageBodies: [...acc.imageBodies, ...result.imageBodies],
      }),
      { questions: [], options: [], imageBodies: [] },
    );
  }

  private createImageBodyData(itemBlockId: number, questionNumber: number) {
    const imageTexts = [
      'รูปภาพแสดงการทำงานของระบบคอมพิวเตอร์',
      'แผนภาพแสดงกระบวนการสังเคราะห์แสงของพืช',
      'ภาพประกอบวงจรน้ำในธรรมชาติ',
      'แผนภูมิแสดงสถิติประชากรโลก',
      'รูปภาพสถาปัตยกรรมไทยโบราณ',
      'แผนที่ประเทศในภูมิภาคเอเชียตะวันออกเฉียงใต้',
      'ภาพตัดขวางของโครงสร้างดิน',
      'แผนภาพแสดงการทำงานของหัวใจ',
      'รูปภาพศิลปกรรมสมัยสุโขทัย',
      'แผนภูมิแสดงวงจรชีวิตของผีเสื้อ',
      'ภาพถ่ายทางอากาศของป่าดิบชื้น',
      'แผนภาพแสดงการเคลื่อนไหวของเปลือกโลก',
      'รูปภาพเครื่องประดับทองคำโบราณ',
      'แผนภูมิแสดงการเปลี่ยนแปลงสภาพภูมิอากาศ',
      'ภาพแสดงความหลากหลายของสิ่งมีชีวิตในทะเล',
    ];

    const index = (questionNumber - 1) % imageTexts.length;

    return this.imageBodyRepository.create({
      itemBlockId,
      imageText: imageTexts[index],
      imagePath: `/mockup/images/question-${questionNumber}.jpg`,
      imageWidth: 800,
      imageHeight: 600,
    });
  }

  private createQuestionData(
    itemBlockId: number,
    type: ItemBlockType,
    questionNumber: number,
  ) {
    const questionTexts = this.getQuestionTextByType(type, questionNumber);
    const scoreByType = {
      [ItemBlockType.RADIO]: 3,
      [ItemBlockType.CHECKBOX]: 4,
      [ItemBlockType.TEXTFIELD]: 10,
      [ItemBlockType.IMAGE]: 5,
    };

    return this.questionRepository.create({
      itemBlockId,
      questionText: questionTexts.main,
      isHeader: true,
      sequence: 1,
      score: scoreByType[type] || 5,
      imagePath: null,
      sizeLimit: null,
      uploadLimit: null,
      acceptFile: null,
    });
  }

  private createOptionsData(
    itemBlockId: number,
    type: ItemBlockType,
    questionNumber: number,
  ): any[] {
    const optionsData = this.getOptionsByType(type, questionNumber);
    return optionsData.map((option, index) =>
      this.optionRepository.create({
        itemBlockId,
        optionText: option.text,
        value: option.value,
        sequence: index + 1,
        imagePath: null,
        nextSection: null,
      }),
    );
  }

  private async createHeaderBlock(assessmentId: number): Promise<void> {
    const headerBlock = this.itemBlockRepository.create({
      sequence: 1,
      section: 1,
      type: ItemBlockType.HEADER,
      isRequired: false,
      assessment: { id: assessmentId } as any,
    });

    const savedHeaderBlock = await this.itemBlockRepository.save(headerBlock);

    // สร้าง HeaderBody
    await this.headerBodyRepository.save({
      title: 'แบบทดสอบความรู้ทั่วไปแบบหลากหลาย',
      description: `100 ข้อ | 2 ชั่วโมง | 420 คะแนน | เกณฑ์ผ่าน 60%
ประเภทคำถาม: ตัวเลือกเดียว (40), หลายข้อ (30), เขียนตอบ (15), รูปภาพ (15)`,
      itemBlockId: savedHeaderBlock.id,
    });
  }

  private getQuestionTextByType(
    type: ItemBlockType,
    questionNumber: number,
  ): { main: string } {
    const questionSets = {
      [ItemBlockType.RADIO]: [
        'ประเทศไทยมีกี่จังหวัด?',
        'เมืองหลวงของประเทศญี่ปุ่นคือ?',
        'สีของธงชาติไทยมีกี่สี?',
        'ภูเขาที่สูงที่สุดในโลกคือ?',
        'มหาสมุทรที่ใหญ่ที่สุดในโลกคือ?',
        'ประเทศที่มีพื้นที่ใหญ่ที่สุดในโลกคือ?',
        'วันเอกราชของประเทศไทยตรงกับวันที่?',
        'พระบาทสมเด็จพระเจ้าอยู่หัวรัชกาลปัจจุบันคือรัชกาลที่?',
        'แม่น้ำที่ยาวที่สุดในโลกคือ?',
        'ทวีปที่มีประเทศมากที่สุดคือ?',
        'องค์ประกอบหลักของอากาศคือ?',
        'ดาวเคราะห์ที่อยู่ใกล้โลกมากที่สุดคือ?',
        'กรุงเทพมหานครก่อตั้งขึ้นในสมัยใด?',
        'สัตว์ประจำชาติไทยคือ?',
        'ไดโนเสาร์สูญพันธุ์เมื่อกี่ล้านปีที่แล้ว?',
        'ประเทศที่มีประชากรมากที่สุดในโลกคือ?',
        'เขตเวลาของประเทศไทยคือ GMT+?',
        'วิตามินที่ร่างกายสังเคราะห์จากแสงแดดคือ?',
        'ธาตุที่มีสัญลักษณ์ Au คือ?',
        'การแพทย์แผนไทยใช้หลักกี่ธาตุ?',
        'คอมพิวเตอร์เครื่องแรกของโลกชื่อ?',
        'อินเทอร์เน็ตเริ่มต้นในปี ค.ศ.?',
        'ระบบปฏิบัติการ Windows ถูกพัฒนาโดยบริษัท?',
        'ภาษาโปรแกรมใดที่ใช้สำหรับเว็บไซต์มากที่สุด?',
        'CPU ย่อมาจาก?',
      ],
      [ItemBlockType.CHECKBOX]: [
        'ข้อใดต่อไปนี้เป็นผลไม้เมืองร้อน? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นภาษาโปรแกรมมิ่ง? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นสีธงชาติไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นวิตามินที่ละลายในน้ำ? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นดาวเคราะห์ในระบบสุริยะ? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นประเทศในอาเซียน? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นส่วนประกอบของคอมพิวเตอร์? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นเบราว์เซอร์? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นระบบฐานข้อมูล? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นโซเชียลมีเดีย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นกีฬาโอลิมปิก? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นเครื่องดนตรีไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นอาหารไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นสถานที่ท่องเที่ยวในไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นมหาวิทยาลัยในไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นภาษาที่ใช้ในการเขียนเว็บ? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นระบบปฏิบัติการ? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นโปรแกรมแก้ไขภาพ? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นการป้องกันไวรัสคอมพิวเตอร์? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นประเภทของเน็ตเวิร์ก? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นหน่วยวัดข้อมูล? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นศิลปะการแสดงไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นพระราชวังในไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นเทศกาลไทย? (เลือกได้หลายข้อ)',
        'ข้อใดเป็นแหล่งพลังงานหมุนเวียน? (เลือกได้หลายข้อ)',
      ],
      [ItemBlockType.TEXTFIELD]: [
        'อธิบายความหมายของคำว่า "ประชาธิปไตย" ในความเข้าใจของคุณ',
        'บรรยายถึงความสำคัญของเทคโนโลยีในชีวิตประจำวัน',
        'อธิบายกระบวนการเกิดฝน',
        'เขียนเกี่ยวกับความสำคัญของป่าไผ่',
        'อธิบายหลักการทำงานของอินเทอร์เน็ต',
        'บรรยายถึงผลกระทบของโลกร้อน',
        'เขียนเกี่ยวกับวัฒนธรรมไทยที่คุณภูมิใจ',
        'อธิบายความสำคัญของการอนุรักษ์สิ่งแวดล้อม',
        'บรรยายถึงบทบาทของเยาวชนในสังคม',
        'เขียนเกี่ยวกับความก้าวหน้าของปัญญาประดิษฐ์',
        'อธิบายกระบวนการสังเคราะห์แสงของพืช',
        'บรรยายถึงความสำคัญของการศึกษา',
        'เขียนเกี่ยวกับประวัติศาสตร์ไทยที่น่าสนใจ',
        'อธิบายระบบเศรษฐกิจพอเพียง',
        'บรรยายถึงผลของการใช้โซเชียลมีเดีย',
        'เขียนเกี่ยวกับความสำคัญของการออกกำลังกาย',
        'อธิบายหลักการทำงานของโซลาร์เซลล์',
        'บรรยายถึงความหลากหลายทางชีวภาพ',
        'เขียนเกี่ยวกับการพัฒนาที่ยั่งยืน',
        'อธิบายกระบวนการรีไซเคิล',
        'บรรยายถึงความสำคัญของน้ำ',
        'เขียนเกี่ยวกับวิถีชีวิตใหม่หลังโควิด',
        'อธิบายหลักการของเศรษฐกิจหมุนเวียน',
        'บรรยายถึงความสำคัญของการอ่าน',
        'เขียนเกี่ยวกับอนาคตของเทคโนโลยี',
      ],
      [ItemBlockType.IMAGE]: [
        'จากรูปภาพ ระบุชื่อของระบบคอมพิวเตอร์ที่แสดง',
        'ขั้นตอนใดในภาพแสดงกระบวนการสังเคราะห์แสงที่สำคัญที่สุด',
        'วงจรน้ำในภาพเริ่มต้นจากขั้นตอนใด',
        'จากแผนภูมิ ประเทศใดมีประชากรมากที่สุด',
        'สถาปัตยกรรมในภาพเป็นแบบสมัยใด',
        'จากแผนที่ ประเทศใดอยู่ตรงกลางภูมิภาคเอเชียตะวันออกเฉียงใต้',
        'ชั้นดินใดในภาพมีความอุดมสมบูรณ์มากที่สุด',
        'การทำงานของหัวใจในภาพแสดงในช่วงใด',
        'ศิลปกรรมในภาพมีลักษณะเด่นอย่างไร',
        'วงจรชีวิตของผีเสื้อในภาพขั้นตอนใดใช้เวลานานที่สุด',
        'ป่าดิบชื้นในภาพมีความหลากหลายทางชีวภาพระดับใด',
        'การเคลื่อนไหวของเปลือกโลกในภาพเกิดจากสาเหตุใด',
        'เครื่องประดับในภาพมีลายลักษณ์แบบใด',
        'การเปลี่ยนแปลงสภาพภูมิอากาศในแผนภูมิแสดงแนวโน้มอย่างไร',
        'สิ่งมีชีวิตในทะเลจากภาพอยู่ในระบบนิเวศใด',
      ],
    };

    const questions = questionSets[type];
    const index = (questionNumber - 1) % questions.length;
    return { main: `${questionNumber}. ${questions[index]}` };
  }

  private getOptionsByType(
    type: ItemBlockType,
    questionNumber: number,
  ): Array<{ text: string; value: number }> {
    if (type === ItemBlockType.RADIO) {
      return this.getRadioOptions(questionNumber);
    } else if (type === ItemBlockType.CHECKBOX) {
      return this.getCheckboxOptions(questionNumber);
    } else if (type === ItemBlockType.IMAGE) {
      return this.getImageOptions(questionNumber);
    }
    return [];
  }

  private getRadioOptions(
    questionNumber: number,
  ): Array<{ text: string; value: number }> {
    const optionSets = [
      [
        { text: '76 จังหวัด', value: 1 },
        { text: '77 จังหวัด', value: 0 },
        { text: '78 จังหวัด', value: 0 },
        { text: '75 จังหวัด', value: 0 },
      ],
      [
        { text: 'โตเกียว', value: 1 },
        { text: 'เกียวโต', value: 0 },
        { text: 'โอซากา', value: 0 },
        { text: 'ฮิโรชิมา', value: 0 },
      ],
      [
        { text: '3 สี', value: 1 },
        { text: '4 สี', value: 0 },
        { text: '5 สี', value: 0 },
        { text: '2 สี', value: 0 },
      ],
      [
        { text: 'เอเวอเรสต์', value: 1 },
        { text: 'K2', value: 0 },
        { text: 'กิลิมันจาโร', value: 0 },
        { text: 'ฟูจิ', value: 0 },
      ],
      [
        { text: 'แปซิฟิก', value: 1 },
        { text: 'แอตแลนติก', value: 0 },
        { text: 'อินเดีย', value: 0 },
        { text: 'อาร์กติก', value: 0 },
      ],
    ];

    const index = (questionNumber - 1) % optionSets.length;
    return optionSets[index];
  }

  private getCheckboxOptions(
    questionNumber: number,
  ): Array<{ text: string; value: number }> {
    const optionSets = [
      [
        { text: 'มะม่วง', value: 1 },
        { text: 'มะละกอ', value: 1 },
        { text: 'แอปเปิล', value: 0 },
        { text: 'ทุเรียน', value: 1 },
      ],
      [
        { text: 'JavaScript', value: 1 },
        { text: 'Python', value: 1 },
        { text: 'Microsoft Word', value: 0 },
        { text: 'Java', value: 1 },
      ],
      [
        { text: 'แดง', value: 1 },
        { text: 'ขาว', value: 1 },
        { text: 'เขียว', value: 0 },
        { text: 'น้ำเงิน', value: 1 },
      ],
      [
        { text: 'วิตามิน C', value: 1 },
        { text: 'วิตามิน B', value: 1 },
        { text: 'วิตามิน A', value: 0 },
        { text: 'วิตามิน D', value: 0 },
      ],
      [
        { text: 'ดาวพุธ', value: 1 },
        { text: 'ดาวศุกร์', value: 1 },
        { text: 'ดวงจันทร์', value: 0 },
        { text: 'ดาวอังคาร', value: 1 },
      ],
    ];

    const index = (questionNumber - 1) % optionSets.length;
    return optionSets[index];
  }

  private getImageOptions(
    questionNumber: number,
  ): Array<{ text: string; value: number }> {
    const optionSets = [
      [
        { text: 'ระบบประมวลผลกลาง (CPU)', value: 1 },
        { text: 'ระบบเก็บข้อมูล (Storage)', value: 0 },
        { text: 'ระบบหน่วยความจำ (RAM)', value: 0 },
        { text: 'ระบบเครือข่าย (Network)', value: 0 },
      ],
      [
        { text: 'การดูดกลืนน้ำและแร่ธาตุ', value: 0 },
        { text: 'การดูดซับแสงแดด', value: 1 },
        { text: 'การคายน้ำ', value: 0 },
        { text: 'การสร้างออกซิเจน', value: 0 },
      ],
      [
        { text: 'การระเหย', value: 1 },
        { text: 'การควบแน่น', value: 0 },
        { text: 'การตก', value: 0 },
        { text: 'การซึมซาบ', value: 0 },
      ],
      [
        { text: 'จีน', value: 1 },
        { text: 'อินเดีย', value: 0 },
        { text: 'สหรัฐอเมริกา', value: 0 },
        { text: 'อินโดนีเซีย', value: 0 },
      ],
      [
        { text: 'สมัยอยุธยา', value: 1 },
        { text: 'สมัยสุโขทัย', value: 0 },
        { text: 'สมัยรัตนโกสินทร์', value: 0 },
        { text: 'สมัยทวารวดี', value: 0 },
      ],
      [
        { text: 'ไทย', value: 1 },
        { text: 'มาเลเซีย', value: 0 },
        { text: 'อินโดนีเซีย', value: 0 },
        { text: 'เวียดนาม', value: 0 },
      ],
      [
        { text: 'ชั้นดินร่วนปน', value: 1 },
        { text: 'ชั้นดินเหนียว', value: 0 },
        { text: 'ชั้นดินทราย', value: 0 },
        { text: 'ชั้นดินลูกรัง', value: 0 },
      ],
      [
        { text: 'ช่วงหัวใจบีบตัว', value: 1 },
        { text: 'ช่วงหัวใจคลายตัว', value: 0 },
        { text: 'ช่วงพักหัวใจ', value: 0 },
        { text: 'ช่วงเต้นปกติ', value: 0 },
      ],
      [
        { text: 'ลายไทยประจำยาม', value: 1 },
        { text: 'ลายไทยแบบธรรมดา', value: 0 },
        { text: 'ลายไทยแบบจีน', value: 0 },
        { text: 'ลายไทยแบบขอม', value: 0 },
      ],
      [
        { text: 'ระยะดักแด้', value: 1 },
        { text: 'ระยะไข่', value: 0 },
        { text: 'ระยะตัวอ่อน', value: 0 },
        { text: 'ระยะผีเสื้อ', value: 0 },
      ],
    ];

    const index = (questionNumber - 1) % optionSets.length;
    return optionSets[index];
  }

  async createMockupSubmissions(
    assessmentId: number,
    submissionCount: number = 50,
  ): Promise<Submission[]> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
      relations: ['itemBlocks', 'itemBlocks.questions', 'itemBlocks.options'],
    });

    if (!assessment) {
      throw new Error('Assessment not found');
    }

    const users = await this.userRepository.find({ take: 50 });
    if (users.length === 0) {
      throw new Error('No users found. Please create some users first.');
    }

    const allQuestions = assessment.itemBlocks
      .flatMap((block) => block.questions)
      .filter((question) => question !== undefined);

    // 🚀 Parallel Processing: สร้าง submissions แบบ parallel chunks
    const chunkSize = 10; // Process 10 submissions per chunk
    const submissionChunks = [];

    for (let i = 0; i < submissionCount; i += chunkSize) {
      const chunk = [];
      const chunkEnd = Math.min(i + chunkSize, submissionCount);

      for (let j = i; j < chunkEnd; j++) {
        const randomUser = users[Math.floor(Math.random() * users.length)];
        const startTime = this.generateRandomStartTime();
        chunk.push(
          this.createSingleSubmissionData(
            assessment,
            randomUser,
            startTime,
            allQuestions,
            j + 1,
          ),
        );
      }

      submissionChunks.push(chunk);
    }

    // 🚀 Process all chunks in parallel
    const allSubmissionPromises = submissionChunks.map((chunk) =>
      Promise.all(chunk),
    );

    const submissionResults = await Promise.all(allSubmissionPromises);
    const flattenedSubmissions = submissionResults.flat();

    // 🚀 Bulk save all submissions and responses
    const submissions = flattenedSubmissions.map((data) => data.submission);
    const savedSubmissions = await this.submissionRepository.save(submissions);

    // 🚀 Bulk save all responses with corrected submission IDs
    const allResponses = flattenedSubmissions.flatMap((data, index) =>
      data.responses.map((response) => ({
        ...response,
        submissionId: savedSubmissions[index].id,
      })),
    );

    if (allResponses.length > 0) {
      await this.responseRepository.save(allResponses);
    }

    return savedSubmissions;
  }

  // 🚀 Helper function for parallel processing
  private createSingleSubmissionData(
    assessment: Assessment,
    user: User,
    startTime: Date,
    allQuestions: Question[],
    submissionIndex: number,
  ): { submission: any; responses: any[] } {
    const completionRate = Math.random();
    const isCompleted = completionRate > 0.1;
    const completionPercentage = isCompleted ? 1 : Math.random() * 0.9 + 0.1;

    const testDuration = Math.floor(
      Math.random() * assessment.timeout * 0.8 + assessment.timeout * 0.2,
    );
    const endTime = new Date(startTime.getTime() + testDuration * 1000);
    const submitTime = isCompleted ? endTime : null;

    const submission = {
      startAt: startTime,
      endAt: endTime,
      submitAt: submitTime,
      userId: user.id,
      assessmentId: assessment.id,
    };

    const questionsToAnswer = Math.floor(
      allQuestions.length * completionPercentage,
    );
    const selectedQuestions = allQuestions
      .sort(() => 0.5 - Math.random())
      .slice(0, questionsToAnswer);

    const responses = this.createResponsesData(
      selectedQuestions,
      assessment,
      submissionIndex,
    );

    return { submission, responses };
  }

  private createResponsesData(
    questions: Question[],
    assessment: Assessment,
    submissionIndex: number,
  ): any[] {
    const responses = [];

    for (const question of questions) {
      const itemBlock = assessment.itemBlocks.find((block) =>
        block.questions.some((q) => q.id === question.id),
      );

      if (!itemBlock) continue;

      const response = this.createResponseData(
        question,
        itemBlock,
        submissionIndex,
      );

      if (response) {
        responses.push(response);
      }
    }

    return responses;
  }

  private createResponseData(
    question: Question,
    itemBlock: ItemBlock,
    submissionIndex: number,
  ): any | null {
    const correctAnswerRate = this.calculateCorrectAnswerRate(submissionIndex);

    const availableOptions = itemBlock.options || [];
    if (availableOptions.length === 0) {
      return null;
    }

    let selectedOptionId: number | null = null;

    if (Math.random() < correctAnswerRate) {
      const correctOptions = availableOptions.filter(
        (option) => option.value === 1,
      );
      if (correctOptions.length > 0) {
        selectedOptionId =
          correctOptions[Math.floor(Math.random() * correctOptions.length)].id;
      }
    } else {
      const incorrectOptions = availableOptions.filter(
        (option) => option.value === 0,
      );
      if (incorrectOptions.length > 0) {
        selectedOptionId =
          incorrectOptions[Math.floor(Math.random() * incorrectOptions.length)]
            .id;
      } else {
        selectedOptionId =
          availableOptions[Math.floor(Math.random() * availableOptions.length)]
            .id;
      }
    }

    return {
      questionId: question.id,
      selectedOptionId,
    };
  }

  private calculateCorrectAnswerRate(submissionIndex: number): number {
    const patterns = [
      0.9, // เก่ง 90%
      0.8, // ดี 80%
      0.7, // ปานกลาง 70%
      0.6, // พอใช้ 60%
      0.5, // อ่อน 50%
      0.4, // แย่ 40%
    ];

    const distribution = [15, 20, 25, 20, 15, 5]; // เปอร์เซ็นต์การกระจาย
    let cumulative = 0;
    const position = (submissionIndex % 100) + 1;

    for (let i = 0; i < distribution.length; i++) {
      cumulative += distribution[i];
      if (position <= cumulative) {
        return patterns[i] + (Math.random() - 0.5) * 0.1;
      }
    }

    return patterns[patterns.length - 1];
  }

  private generateRandomStartTime(): Date {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const randomTime =
      thirtyDaysAgo.getTime() +
      Math.random() * (now.getTime() - thirtyDaysAgo.getTime());

    return new Date(randomTime);
  }

  async createCompleteAssessmentWithSubmissions(
    submissionCount: number = 50,
  ): Promise<{
    assessment: Assessment;
    submissions: Submission[];
  }> {
    // สร้าง assessment
    const assessment = await this.createMockupQuizAssessment();

    // สร้าง submissions
    const submissions = await this.createMockupSubmissions(
      assessment.id,
      submissionCount,
    );

    return {
      assessment,
      submissions,
    };
  }
}
