/// <reference types="cypress" />

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      logout(): Chainable<void>;
    }
  }
}

Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('http://localhost:9000/login');
  cy.get('[data-cy="login_username"]').type(email);
  cy.get('[data-cy="login_password"]').type(password);
  cy.get('[data-cy="login_btn"]').click();
});

Cypress.Commands.add('logout', () => {
  cy.get('.q-avatar__content > img').click();
  cy.get('.q-item--clickable').contains('ออกจากระบบ').click();
});
