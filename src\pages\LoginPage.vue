<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import { useAuthStore } from 'src/stores/auth';
import utilsConfigs from 'src/configs/utilsConfigs';

import { QIcon, QInput, QBtn, QForm } from 'quasar';
import { useRouter } from 'vue-router';
import { nextTick } from 'vue';

const router = useRouter();
const visiblePassword = ref(false);
const authStore = useAuthStore();

const form = ref<QForm | null>(null);

const login = async () => {
  await nextTick();
  const valid = await form.value?.validate();
  if (valid) {
    const success = await authStore.loginBuu();
    if (success) {
      const redirectPath = localStorage.getItem('redirectAfterLogin');
      if (redirectPath) {
        localStorage.removeItem('redirectAfterLogin');
        await router.push(redirectPath);
      } else {
        await router.push('/');
      }
    }
  }
};

onUnmounted(() => {
  authStore.loginUsername = '';
  authStore.loginPassword = '';
});
</script>

<template>
  <q-page class="row">
    <div class="col flex bg-secondary">
      <q-icon name="tv" size="300px" color="white" style="opacity: 0.737; margin: auto"></q-icon>
      <div class="absolute text-white" style="inset: 0; left: 2em; top: 95%">
        Crafted by the computer center of Burapha University. © 2025 Copyright All Rights Reserved.
      </div>
    </div>

    <div class="col-auto q-my-auto">
      <div style="width: 600px" class="flex justify-center row">
        <q-icon
          name="swap_horiz"
          size="200px"
          color="primary"
          class="q-mx-auto q-mb-md col-12"
        ></q-icon>
        <div style="width: 400px" class="col-12">
          <q-form ref="form" @submit.prevent="login()" class="q-gutter-y-lg">
            <q-input
              color="accent"
              dense
              outlined
              data-cy="login_username"
              label="ชื่อผู้ใช้"
              v-model="authStore.loginUsername"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกชื่อผู้ใช้']"
              @keyup.enter="login"
            >
              <template v-slot:prepend>
                <q-icon name="account_circle" :color="utilsConfigs.colorSystem"></q-icon>
              </template>
            </q-input>
            <q-input
              color="accent"
              :type="visiblePassword ? 'text' : 'password'"
              dense
              outlined
              data-cy="login_password"
              label="รหัสผ่าน"
              v-model="authStore.loginPassword"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกรหัสผ่าน']"
              @keyup.enter="login"
            >
              <template v-slot:prepend>
                <q-icon name="lock" :color="utilsConfigs.colorSystem"></q-icon>
              </template>
              <template v-slot:append>
                <q-icon
                  :data-cy="visiblePassword ? 'i-eye' : 'i-eyeOff'"
                  :name="visiblePassword ? 'visibility' : 'visibility_off'"
                  :color="utilsConfigs.colorSystem"
                  @click="visiblePassword = !visiblePassword"
                  class="cursor-pointer"
                ></q-icon>
              </template>
            </q-input>
            <q-btn
              type="submit"
              unelevated
              dense
              class="text-black full-width text-body1"
              data-cy="login_btn"
              label="เข้าสู่ระบบ"
              color="primary"
            >
            </q-btn>
          </q-form>
          <div class="q-mt-lg flex justify-center">
            <a href="https://myid.buu.ac.th/" class="text-accent q-mt-sm font-weight-regular"
              >ลืมรหัสผ่าน ?</a
            >
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>
