<template>
  <div class="grid-container">
    <div v-for="(dataset, index) in props.data" :key="index" class="grid-item">
      <div>
        <a :href="dataset.label" target="_blank">
          <q-img
            :src="dataset.thumbnail || getIcon(dataset)"
            class="image-container"
            :class="{ 'is-icon': !dataset.thumbnail }"
            :ratio="4 / 3"
            fit="cover"
            :loading="dataset.thumbnail ? 'eager' : 'lazy'"
          />
        </a>
        <div class="label">{{ props.labels[index] }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Dataset {
  label: string;
  values: number[];
  thumbnail?: string;
}

const props = defineProps<{
  labels: string[];
  data: Dataset[];
}>();

const icons: Record<string, string> = {
  pdf: '/file-icons/pdf.webp',
  docx: '/file-icons/docx.webp',
  pptx: '/file-icons/pptx.webp',
  xlsx: '/file-icons/xlsx.webp',
  csv: '/file-icons/csv.webp',
  mp3: '/file-icons/mp3.webp',
  mp4: '/file-icons/mp4.webp',
};

// Store generated thumbnails
const thumbnails = ref<Record<string, string>>({});

const generateThumbnail = async (url: string): Promise<string> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.src = url;
    video.crossOrigin = 'anonymous';
    video.muted = true;
    video.preload = 'metadata';

    video.onloadeddata = () => {
      video.currentTime = 1;
    };

    video.onseeked = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 200;
      canvas.height = 150;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const thumbnailUrl = canvas.toDataURL('image/jpeg');
        resolve(thumbnailUrl);
      } else {
        resolve(icons.mp4!);
      }
      video.remove();
    };

    video.onerror = () => {
      resolve(icons.mp4!);
    };

    video.load();
  });
};

const getIcon = (dataset: Dataset): string => {
  const cleanUrl = dataset.label.split('?')[0]!.toLowerCase();
  const ext = cleanUrl.split('.').pop() || '';

  if (ext === 'mp4') {
    return thumbnails.value[dataset.label] ?? icons.mp4 ?? '';
  }

  return icons[ext] ?? dataset.label;
};

// Generate thumbnails on mount
onMounted(async () => {
  for (const dataset of props.data) {
    const cleanUrl = dataset.label.split('?')[0]!.toLowerCase();
    const ext = cleanUrl.split('.').pop() || '';
    if (ext === 'mp4' && !dataset.thumbnail && !thumbnails.value[dataset.label]) {
      const thumbnail = await generateThumbnail(dataset.label);
      thumbnails.value[dataset.label] = thumbnail;
    }
  }
});
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 200px);
  gap: 50px;
  padding: 30px;
  justify-content: center;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.grid-item > div {
  overflow: hidden;
  border: 2px solid #474747;
  border-radius: 21px;
}

.image-container {
  width: 200px;
  height: 150px;
  border-radius: 12px;
  background-color: #fdfdfd;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-container.is-icon img {
  width: 50px;
  height: 100px;
  object-fit: contain;
  border-radius: 8px;
}

.label {
  margin-top: 10px;
  font-size: 14px;
  text-align: center;
  color: #333;
  padding-bottom: 10px;
}
</style>
