import type { QTableProps } from 'quasar';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { Permission } from 'src/types/models';
import { formatParams } from 'src/utils/utils';

export class PermissionService {
  private static path = 'permissions';

  static createPermission(dto: Permission) {
    return api.post<Permission>(`${this.path}`, dto);
  }

  static getPermissionById(id: number) {
    return api.get<Permission>(`${this.path}/${id}`);
  }

  static updatePermission(id: number, data: Partial<Permission>) {
    return api.patch<Permission>(`${this.path}/${id}`, data);
  }

  static deletePermission(id: number) {
    return api.delete<Permission>(`${this.path}/${id}`);
  }

  static getPermissions(pagination: QTableProps['pagination']) {
    const params = formatParams(pagination);
    return api.get<DataResponse<Permission>>(`${this.path}`, { params });
  }
}
