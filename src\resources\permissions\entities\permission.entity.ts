import { Role } from 'src/resources/roles/entities/role.entity';
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
} from 'typeorm';

@Entity('permissions')
export class Permission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  nameEn: string;

  @Column({ default: true })
  status: boolean;

  @Column({ default: false })
  isDefault: boolean;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];
}
