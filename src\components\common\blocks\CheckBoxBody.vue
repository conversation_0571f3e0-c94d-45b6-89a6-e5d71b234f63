<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Drag Indicator, Checkbox, Text Field) -->
    <div
      v-for="(_choice, index) in store.checkboxOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event, true)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        class="q-mr-sm"
        @mouseover="store.hoverRow(index)"
      />

      <q-checkbox
        v-model="store.checkboxSelectedOptions"
        :val="store.checkboxOptions[index]!.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="row items-center q-col-gutter-sm no-wrap">
        <!-- Option text input -->
        <div class="col-10">
          <q-input
            v-model="store.checkboxOptions[index]!.optionText"
            :placeholder="store.checkboxOptions[index]!.placeholder"
            dense
            @update:model-value="() => handleOptionTextChange(index)"
            @blur="handleOptionTextBlur(index)"
            class="option-text-input"
            style="min-width: 480px; flex: 1"
          />
        </div>
        <!-- Value input -->
        <div class="col-2">
          <q-input
            v-if="props.type === 'quiz'"
            v-model.number="store.checkboxOptions[index]!.score"
            type="number"
            dense
            class="value-input"
            @input="updateOptionValue(index)"
            @blur="handleOptionScoreBlur(index, $event)"
          />
        </div>
      </div>

      <div class="col-auto">
        <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      </div>

      <div class="col-auto">
        <q-btn
          v-if="store.checkboxOptions.length > 1"
          flat
          round
          icon="close"
          @click="handleDeleteOption(index)"
          :disable="store.checkboxOptions.length <= 1"
          class="q-ml-sm"
        />
      </div>
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Option } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';

// Define interface for option update data
interface OptionUpdateData {
  index: number;
  option: Option;
}

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('CheckBoxAns must be used within an ItemBlock component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Define emits for updating parent component
const emit = defineEmits<{
  'update:options': [options: Option[]];
  'option:created': [option: Option];
  'option:updated': [optionId: number, data: OptionUpdateData];
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'value',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Handler for option text changes (real-time updates without saving)
const handleOptionTextChange = (index: number) => {
  // Update the store immediately for UI responsiveness
  store.updateOption(index, true); // true for checkbox
  // No auto-save here - save only on blur
};

// Handler for option text blur (save on blur)
const handleOptionTextBlur = async (index: number) => {
  const storeOption = store.checkboxOptions[index];
  const optionText = storeOption?.optionText || '';

  console.log('🔍 [CHECKBOX] Option text blur - checking option state:', {
    index,
    storeOptionId: storeOption?.id,
    optionText,
    itemBlockOptionsLength: props.itemBlock.options?.length || 0,
  });

  // If store option doesn't have an ID yet, it needs to be created first
  if (storeOption && !storeOption.id) {
    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        console.log('🚀 [CHECKBOX] Creating new option via blur for index:', index);

        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: storeOption.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Update the store with the backend-generated ID
        if (store.checkboxOptions[index]) {
          store.checkboxOptions[index].id = createdOption.id;
        }

        console.log('✅ [CHECKBOX] Option created via blur, updated store with ID:', {
          index,
          optionId: createdOption.id,
          storeOptionId: store.checkboxOptions[index]?.id,
        });

        // Emit option update to parent component
        emit('option:updated', createdOption.id, {
          index,
          option: createdOption,
        });
      } catch (error) {
        console.error('❌ Failed to create checkbox option on blur:', error);
      }
    }
  } else if (storeOption && storeOption.id && autoSave) {
    // Option exists, trigger save via parent component
    console.log('💾 [CHECKBOX] Triggering auto-save for existing option:', {
      optionId: storeOption.id,
      optionText,
    });
    autoSave.triggerOptionAutoSave(storeOption.id, 'optionText', optionText);
  }
};

// Handler for option score blur (save on blur)
const handleOptionScoreBlur = async (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = Number(target.value) || 0;
  const storeOption = store.checkboxOptions[index];

  console.log('🔍 [CHECKBOX] Option score blur - checking option state:', {
    index,
    storeOptionId: storeOption?.id,
    newValue,
    itemBlockOptionsLength: props.itemBlock.options?.length || 0,
  });

  if (storeOption && storeOption.id && autoSave) {
    // For score changes, we need to update the value field
    console.log('💾 [CHECKBOX] Triggering auto-save for option score:', {
      optionId: storeOption.id,
      newValue,
    });
    autoSave.triggerOptionAutoSave(storeOption.id, 'value', newValue);
  } else if (storeOption && storeOption.id) {
    // Fallback: use optionService directly if auto-save is not available
    try {
      console.log('🔄 [CHECKBOX] Direct update for option score:', {
        optionId: storeOption.id,
        newValue,
      });
      await optionService.updateOption(storeOption.id, {
        optionText: storeOption.optionText,
        itemBlockId: props.itemBlock.id,
        value: newValue,
      });
    } catch (error) {
      console.error('Failed to update checkbox option score:', error);
    }
  }
};

// Handler for deleting a checkbox option
const handleDeleteOption = async (index: number) => {
  try {
    // Get the option from the store (not from props)
    const storeOption = store.checkboxOptions[index];

    console.log('🗑️ [CHECKBOX] Deleting option at index:', {
      index,
      storeOptionId: storeOption?.id,
      storeOptionText: storeOption?.optionText,
      allStoreOptionIds: store.checkboxOptions.map((opt, idx) => ({
        index: idx,
        id: opt.id,
        text: opt.optionText,
      })),
    });

    // If option has an ID, delete it from backend first
    if (storeOption && storeOption.id) {
      console.log('🚀 [CHECKBOX] Sending delete request for option ID:', storeOption.id);
      await optionService.removeOption(storeOption.id);
      console.log('✅ [CHECKBOX] Option deleted from backend successfully');
    } else {
      console.log('⚠️ [CHECKBOX] Option has no ID, skipping backend deletion');
    }

    // Remove from local store (true for checkbox)
    store.removeOption(index, true);
    console.log('✅ [CHECKBOX] Option removed from local store');
  } catch (error) {
    console.error('❌ Failed to delete checkbox option:', error);
  }
};

// Handler for adding new checkbox option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // Log context information
    console.log('🎯 [CHECKBOX] Adding new checkbox option for ItemBlock:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      sequence: props.itemBlock.sequence,
      section: props.itemBlock.section,
      currentOptionsCount: store.checkboxOptions.length,
    });

    // First add to local store for immediate UI feedback
    store.addOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    console.log(
      '📤 [CHECKBOX] Sending checkbox option creation request:',
      JSON.stringify(optionData, null, 2),
    );

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Log successful creation with full context
    console.log(
      '✅ [CHECKBOX] Checkbox option created successfully:',
      JSON.stringify(
        {
          createdOption,
          context: {
            itemBlockId: props.itemBlock.id,
            assessmentId: props.itemBlock.assessmentId,
            sequence: props.itemBlock.sequence,
            section: props.itemBlock.section,
            newOptionIndex,
            totalOptionsAfterCreation: store.checkboxOptions.length,
          },
        },
        null,
        2,
      ),
    );

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data including the ID
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].id = createdOption.id;
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });

      console.log(
        '📡 [CHECKBOX] Emitted option:created event with data:',
        JSON.stringify(
          {
            id: createdOption.id,
            itemBlockId: createdOption.itemBlockId,
            optionText: createdOption.optionText,
            value: createdOption.value,
            sequence: createdOption.sequence,
          },
          null,
          2,
        ),
      );
    }
  } catch (error) {
    console.error('❌ [CHECKBOX] Failed to create checkbox option:', error);
    console.error('❌ [CHECKBOX] Context during failure:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      optionsCount: store.checkboxOptions.length,
    });

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" checkbox option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // Log context information
    console.log('🎯 [CHECKBOX-OTHER] Adding new "other" checkbox option for ItemBlock:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      sequence: props.itemBlock.sequence,
      section: props.itemBlock.section,
      currentOptionsCount: store.checkboxOptions.length,
    });

    // First add to local store for immediate UI feedback
    store.addOtherOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    console.log(
      '📤 [CHECKBOX-OTHER] Sending "other" checkbox option creation request:',
      JSON.stringify(optionData, null, 2),
    );

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Log successful creation with full context
    console.log(
      '✅ [CHECKBOX-OTHER] "Other" checkbox option created successfully:',
      JSON.stringify(
        {
          createdOption,
          context: {
            itemBlockId: props.itemBlock.id,
            assessmentId: props.itemBlock.assessmentId,
            sequence: props.itemBlock.sequence,
            section: props.itemBlock.section,
            newOptionIndex,
            totalOptionsAfterCreation: store.checkboxOptions.length,
          },
        },
        null,
        2,
      ),
    );

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data including the ID
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].id = createdOption.id;
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });

      console.log(
        '📡 [CHECKBOX-OTHER] Emitted option:created event with data:',
        JSON.stringify(
          {
            id: createdOption.id,
            itemBlockId: createdOption.itemBlockId,
            optionText: createdOption.optionText,
            value: createdOption.value,
            sequence: createdOption.sequence,
          },
          null,
          2,
        ),
      );
    }
  } catch (error) {
    console.error('❌ [CHECKBOX-OTHER] Failed to create "other" checkbox option:', error);
    console.error('❌ [CHECKBOX-OTHER] Context during failure:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      optionsCount: store.checkboxOptions.length,
    });

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }
  } finally {
    isCreatingOption.value = false;
  }
};

// Function to update option value (score) - real-time updates without saving
const updateOptionValue = (index: number) => {
  // Ensure the score is a number
  const score = store.checkboxOptions[index]!.score;
  if (typeof score !== 'number') {
    store.checkboxOptions[index]!.score = Number(score) || 0;
  }

  // Update store for real-time UI updates
  store.updateOption(index, true);
  // No auto-save here - save only on blur
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}

.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}

.option-text-input {
  width: 100%;
}

.value-input {
  width: 100%;
  max-width: 120px;
}

.full-width {
  width: 100%;
}
</style>
