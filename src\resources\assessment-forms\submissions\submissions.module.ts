import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubmissionsService } from './submissions.service';
import { Submission } from '../entities/submission.entity';
import { Response } from '../entities/response.entity';
import { SubmissionsController } from './submissions.controller';
import { Assessment } from '../entities/assessment.entity';
import { QuizHelperService } from '../services/quiz-helper.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Submission,
      Response, // <-- Added Response entity
      Assessment, // <-- Added Assessment entity for QuizHelperService
    ]),
  ],
  controllers: [SubmissionsController],
  providers: [SubmissionsService, QuizHelperService],
  exports: [SubmissionsService],
})
export class SubmissionsModule {}
