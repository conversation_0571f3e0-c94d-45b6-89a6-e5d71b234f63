import { PartialType, ApiProperty } from '@nestjs/swagger';
import { CreateResponseDto } from '../creates/create-response.dto';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateResponseDto extends PartialType(CreateResponseDto) {
  @ApiProperty({
    description: 'ID of the response to update',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  id?: number;

  @IsNumber()
  @IsOptional()
  submissionId?: number;

  @IsNumber()
  @IsOptional()
  questionId?: number;

  @IsNumber()
  @IsOptional()
  selectedOptionId?: number;
}
