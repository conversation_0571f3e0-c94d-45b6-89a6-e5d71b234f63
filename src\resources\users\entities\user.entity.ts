import { Assessment } from 'src/resources/assessment-forms/entities/assessment.entity';
import { Submission } from 'src/resources/assessment-forms/entities/submission.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import { Role } from 'src/resources/roles/entities/role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Exclude } from 'class-transformer';

@Entity('users') 
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ unique: true, length: 100 })
  email: string;

  @Column()
  @Exclude() // Exclude password from serialization
  password: string;

  @ManyToMany(() => Role, (role) => role.users, { eager: true })
  @JoinTable({
    name: 'users_has_roles', 
    joinColumn: { name: 'userId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'roleId', referencedColumnName: 'id' },
  })
  roles: Role[];

  @OneToMany(() => Program, (program) => program.creator)
  programs: Program[];

  @OneToMany(() => Assessment, (assessment) => assessment.creator)
  assessments: Assessment[];

  @OneToMany(() => Submission, (submission) => submission.user)
  submissions: Submission[];
}
