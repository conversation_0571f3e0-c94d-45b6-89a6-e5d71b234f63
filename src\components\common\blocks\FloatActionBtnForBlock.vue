<template>
  <q-card class="float-btn q-ml-md" :class="{ 'quiz-mode': props.type === 'quiz' }">
    <div>
      <q-btn
        icon="app:add-circle"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        :loading="disabled"
        @click="$emit('add')"
      >
      </q-btn>

      <q-btn
        icon="app:text"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        @click="$emit('add-text')"
      >
      </q-btn>

      <q-btn
        icon="app:image"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        @click="openUploadDialog"
      >
      </q-btn>

      <q-btn
        v-if="props.type !== 'quiz'"
        icon="app:section"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        @click="$emit('add-section')"
      >
      </q-btn>
    </div>

    <UploadImage v-model="showUploadDialog" @image-selected="handleImageSelected" />
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadImage from '../UploadImage.vue';

const props = defineProps<{
  disabled?: boolean;
  type?: 'quiz' | 'evaluate';
}>();

const emit = defineEmits(['add', 'add-text', 'add-section', 'add-image']);
const showUploadDialog = ref(false);

function openUploadDialog() {
  showUploadDialog.value = true;
}

function handleImageSelected(imageData: string) {
  emit('add-image', imageData);
}
</script>

<style scoped>
.float-btn {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
  height: 190px;
  width: 48px;
}

.float-btn.quiz-mode {
  height: 150px; /* Reduced height for quiz mode (3 buttons instead of 4) */
}
</style>
