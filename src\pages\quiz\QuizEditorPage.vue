<template>
  <q-page class="bg-page-primary">
    <!-- แสดง tabs เฉพาะเมื่อไม่ใช่ preview mode -->
    <template v-if="!isPreviewMode">
      <AsmMenuTab :menu="defaultAsmTabsMenu" v-model="selectedTab" />
      <q-tab-panels v-model="selectedTab" animated>
        <q-tab-panel
          v-for="tab in defaultAsmTabsMenu"
          :name="tab.name ?? ''"
          :key="tab.name ?? ''"
          class="bg-page-primary"
        >
          <component :is="componentMap[tab.name as string]" v-if="selectedTab === tab.name" />
        </q-tab-panel>
      </q-tab-panels>
    </template>

    <template v-else>
      <component :is="componentMap.questions" />
    </template>
  </q-page>
</template>

<script setup lang="ts">
import AsmMenuTab from 'src/components/common/AsmMenuTab.vue';
import { defaultAsmTabsMenu } from 'src/data/menu';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';
import { onMounted, ref, defineAsyncComponent, computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const id = route.params.id;
const hash = route.hash;
const asm = useAssessmentStore();
const selectedTab = ref('');

const isPreviewMode = computed(() => route.params.mode === 'preview');
onMounted(() => {
  if (hash) {
    selectedTab.value = hash.replace('#', '');
  } else {
    selectedTab.value = 'questions';
  }
});

const componentMap: Record<string, ReturnType<typeof defineAsyncComponent>> = {
  questions: defineAsyncComponent(() => import('./tabs/QuizEditView.vue')),
  replies: defineAsyncComponent(() => import('./tabs/QuizReplyView.vue')),
  settings: defineAsyncComponent(() => import('./tabs/QuizSettingView.vue')),
};

onMounted(async () => {
  const data = await new AssessmentService('quiz').fetchOne(Number(id));
  asm.setCurrentAssessment(data);
});
</script>

<style scoped></style>
