import type { BlockBodyOptionsType } from 'src/types/app';

export const blockBodyOptions: BlockBodyOptionsType[] = [
  { label: 'หลายตัวเลือก', value: 'RADIO', icon: 'radio_button_checked' },
  { label: 'ช่องทำเครื่องหมาย', value: 'CHECKBOX', icon: 'check_box' },
  { label: 'แบบเขียน', value: 'TEXTFIELD', icon: 'short_text' },
  { label: 'ตารางกริดหลายตัวเลือก', value: 'GRID', icon: 'grid_on' },
  { label: 'อัปโหลดไฟล์', value: 'UPLOAD', icon: 'cloud_upload' },
];

export const blockBodyOptionsMap: Record<string, BlockBodyOptionsType> = Object.fromEntries(
  blockBodyOptions.map((option) => [option.label, option]),
);
