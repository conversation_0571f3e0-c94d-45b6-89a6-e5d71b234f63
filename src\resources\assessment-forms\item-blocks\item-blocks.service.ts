import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateItemBlockDto } from '../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../dto/updates/update-item-block.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { ItemBlock } from '../entities/item-block.entity';
import { In, Repository, type EntityManager } from 'typeorm';
import { QuestionsService } from '../questions/questions.service';
import { ItemBlockType } from '../enums/item-block-type.enum';
import type { UpdateItemBlockSequencesDto } from '../dto/updates/ีupdate-block-sequence.dto';
import { OptionsService } from '../options/options.service';
import { Assessment } from '../entities/assessment.entity';
import { HeaderBody } from '../entities/header-body.entity';
import { ImageBody } from '../entities/image-body.entity';
import { Submission } from '../entities/submission.entity';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';
import { AssessmentConfig } from 'src/configs/assessment.config';

@Injectable()
export class ItemBlocksService {
  constructor(
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private readonly questionService: QuestionsService,
    private readonly optionsService: OptionsService,
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(Option)
    private readonly optionRepository: Repository<Option>,
    @InjectRepository(HeaderBody)
    private readonly headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ImageBody)
    private readonly imageBodyRepository: Repository<ImageBody>,
  ) {}

  async getNextSequence(assessmentId: number): Promise<number> {
    const lastItemBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.sequence', 'DESC')
      .getOne();

    return lastItemBlock ? lastItemBlock.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async handleType(type: string, savedItemBlock: ItemBlock) {
    const itemBlockType = type as ItemBlockType;

    // Handle Question-based types (RADIO, CHECKBOX, GRID, TEXTFIELD)
    if (this.isQuestionBasedType(itemBlockType)) {
      await this.createQuestionsForBlock(savedItemBlock.id, itemBlockType);

      // textfield type does not need options
      if (this.needsOptions(itemBlockType)) {
        await this.createDefaultOption(savedItemBlock.id);
      }
    }
    // Handle Header type
    else if (itemBlockType === ItemBlockType.HEADER) {
      await this.createHeaderBody(savedItemBlock.id);
    }
    // Handle Image type
    else if (itemBlockType === ItemBlockType.IMAGE) {
      await this.createImageBody(savedItemBlock.id);
    }

    return savedItemBlock;
  }

  private isQuestionBasedType(type: ItemBlockType): boolean {
    return ![ItemBlockType.HEADER, ItemBlockType.IMAGE].includes(type);
  }

  private needsOptions(type: ItemBlockType): boolean {
    return [
      ItemBlockType.RADIO,
      ItemBlockType.CHECKBOX,
      ItemBlockType.GRID,
    ].includes(type);
  }

  private async createQuestionsForBlock(
    itemBlockId: number,
    type: ItemBlockType,
  ): Promise<void> {
    // GRID type needs both header (false) and main (true) questions
    if (type === ItemBlockType.GRID) {
      await this.questionService.create({
        itemBlockId,
        isHeader: false,
      });
    }

    // All question-based types need a main question
    await this.questionService.create({
      itemBlockId,
      isHeader: true,
    });
  }

  private async createDefaultOption(itemBlockId: number): Promise<void> {
    await this.optionsService.create({
      itemBlockId,
      value: 1,
      sequence: 1,
      nextSection: 1,
    });
  }

  private async createHeaderBody(itemBlockId: number): Promise<void> {
    const headerBody = this.headerBodyRepository.create({
      title: AssessmentConfig.DEFAULT_HEADER_TITLE,
      description: AssessmentConfig.DEFAULT_HEADER_DESCRIPTION,
      itemBlockId,
    });
    await this.headerBodyRepository.save(headerBody);
  }

  private async createImageBody(itemBlockId: number): Promise<void> {
    const imageBody = this.imageBodyRepository.create({
      itemBlockId,
    });
    await this.imageBodyRepository.save(imageBody);
  }

  // async createHeaderTemplate(assessmentId: number) {
  //   const nextSequence =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedHeaderBlock, body: savedHeaderBody } =
  //     await this.createHeaderBlockWithBody(
  //       assessmentId,
  //       nextSequence,
  //       nextSection,
  //     );
  //   const nextSequence2 =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection2 =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedHeaderBlock2, body: savedHeaderBody2 } =
  //     await this.createHeaderBlockWithBody(
  //       assessmentId,
  //       nextSequence2,
  //       nextSection2,
  //     );

  //   return {
  //     id: savedHeaderBlock.id,
  //     sequence: savedHeaderBlock.sequence,
  //     section: savedHeaderBlock.section,
  //     type: savedHeaderBlock.type,
  //     headerBody: {
  //       id: savedHeaderBody.id,
  //       title: savedHeaderBody.title,
  //       description: savedHeaderBody.description,
  //     },
  //   };
  // }

  // async createQuestionTemplate(assessmentId: number) {
  //   const nextSequence =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedQuestionBlock, body: savedQuestionBody } =
  //     await this.createQuestionBlockWithBody(
  //       assessmentId,
  //       nextSequence,
  //       nextSection,
  //     );

  //   const { body: savedOptionBlock } = await this.createOption(
  //     savedQuestionBlock.id,
  //   );

  //   return {
  //     id: savedQuestionBlock.id,
  //     sequence: savedQuestionBlock.sequence,
  //     section: savedQuestionBlock.section,
  //     type: savedQuestionBlock.type,
  //     isRequired: savedQuestionBlock.isRequired,
  //     question: {
  //       id: savedQuestionBody.id,
  //       questionText: savedQuestionBody.questionText,
  //       imagePath: savedQuestionBody.imagePath,
  //       isHeader: savedQuestionBody.isHeader,
  //       sequence: savedQuestionBody.sequence,
  //       sizeLimit: savedQuestionBody.sizeLimit,
  //       acceptFile: savedQuestionBody.acceptFile,
  //       uploadLimit: savedQuestionBody.uploadLimit,
  //     },
  //     options: [
  //       {
  //         id: savedOptionBlock.id,
  //         optionText: savedOptionBlock.optionText,
  //         imagePath: savedOptionBlock.imagePath,
  //         value: savedOptionBlock.value,
  //         sequence: savedOptionBlock.sequence,
  //         nextSection: savedOptionBlock.nextSection,
  //       },
  //     ],
  //   };
  // }

  async createEmptyBlock({
    assessmentId,
    sequence,
    section,
    type,
  }: {
    assessmentId: number;
    sequence?: number;
    section?: number;
    type?: ItemBlockType;
  }): Promise<ItemBlock> {
    //this method will create a block with no body
    const block = this.itemBlockRepository.create({
      sequence,
      section,
      type: type,
      isRequired: false,
      assessment: { id: assessmentId },
    });

    return await this.itemBlockRepository.save(block);
  }

  /**
   * Updates the sequence of multiple item blocks simultaneously using a safe and optimized bulk update approach
   * @param updateSequencesDto DTO containing an array of item blocks with their new sequence numbers
   * @returns Object with success status and updated item blocks
   */
  async updateSequences(updateSequencesDto: UpdateItemBlockSequencesDto) {
    const { itemBlocks } = updateSequencesDto;

    if (!itemBlocks || itemBlocks.length === 0) {
      throw new BadRequestException(
        'No item blocks provided for sequence update',
      );
    }

    const itemBlockIds = itemBlocks.map((item) => item.id);

    const queryRunner =
      this.itemBlockRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
        select: ['id', 'sequence', 'assessmentId'],
      });

      if (existingBlocks.length !== itemBlockIds.length) {
        const foundIds = existingBlocks.map((block) => block.id);
        const missingIds = itemBlockIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Item blocks with IDs ${missingIds.join(', ')} not found`,
        );
      }

      const caseStatements = itemBlocks
        .map(() => `WHEN id = ? THEN ?`)
        .join(' ');

      const wherePlaceholders = itemBlocks.map(() => `?`).join(',');

      // สร้าง params: [id1, seq1, id2, seq2, ..., idN, seqN, id1, id2, ..., idN]
      const params: any[] = [];
      itemBlocks.forEach((item) => {
        params.push(item.id, item.sequence);
      });
      itemBlocks.forEach((item) => {
        params.push(item.id);
      });

      const rawQuery = `
    UPDATE item_blocks
    SET sequence = CASE ${caseStatements} ELSE sequence END
    WHERE id IN (${wherePlaceholders})
  `;

      await queryRunner.manager.query(rawQuery, params);

      await queryRunner.commitTransaction();

      const updatedBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
      });

      return {
        success: true,
        message: 'Item block sequences updated successfully',
        data: updatedBlocks.map((block) => ({
          id: block.id,
          sequence: block.sequence,
        })),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        `Failed to update item block sequences: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async createBlock(dto: CreateItemBlockDto) {
    const { assessmentId, type, ...rest } = dto;

    // Validate assessment exists
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
    });
    if (!assessment) {
      throw new NotFoundException(
        `Assessment with ID ${assessmentId} not found`,
      );
    }

    // Create and save the main ItemBlock
    const itemBlock = this.itemBlockRepository.create({
      ...rest,
      isRequired: rest.isRequired ?? false,
      type,
      sequence: await this.getNextSequence(assessmentId),
      assessment,
    });

    const savedItemBlock = await this.itemBlockRepository.save(itemBlock);

    // Create related entities based on type
    await this.handleType(type, savedItemBlock);

    // Load and return complete item block with all relations
    return this.loadCompleteItemBlock(savedItemBlock.id);
  }

  private async loadCompleteItemBlock(itemBlockId: number) {
    const [itemBlock, questions, options, headerBody, imageBody] =
      await Promise.all([
        this.itemBlockRepository.findOne({
          where: { id: itemBlockId },
          relations: ['assessment'],
        }),
        this.questionRepository.find({
          where: { itemBlock: { id: itemBlockId } },
        }),
        this.optionRepository.find({
          where: { itemBlock: { id: itemBlockId } },
        }),
        this.headerBodyRepository.findOne({
          where: { itemBlockId },
        }),
        this.imageBodyRepository.findOne({
          where: { itemBlockId },
        }),
      ]);

    return {
      id: itemBlock.id,
      sequence: itemBlock.sequence,
      section: itemBlock.section,
      type: itemBlock.type,
      isRequired: itemBlock.isRequired,
      assessmentId: itemBlock.assessment.id,
      questions: questions.length > 0 ? questions : null,
      options: options.length > 0 ? options : null,
      headerBody: headerBody ?? null,
      imageBody: imageBody ?? null,
    };
  }

  // async createBlockHeader(dto: CreateItemBlockDto) {
  //   const assessment = await this.assessmentRepo.findOne({
  //     where: { id: dto.assessmentId },
  //   });
  //   const itemBlock = this.itemBlockRepository.create({
  //     type: ItemBlockType.HEADER,
  //     sequence: dto.sequence ?? 1,
  //     section: dto.section ?? 1,
  //     isRequired: false,
  //     assessment,
  //   });
  //   const savedBlock = await this.itemBlockRepository.save(itemBlock);
  //   const headerBody = await this.headerBodiesService.createHeader({
  //     itemBlockId: savedBlock.id,
  //   });

  //   return {
  //     id: savedBlock.id,
  //     type: savedBlock.type,
  //     sequence: savedBlock.sequence,
  //     section: savedBlock.section,
  //     isRequired: savedBlock.isRequired,
  //     assessmentId: savedBlock.assessment.id,
  //     headerBody,
  //   };
  // }

  findAll(assessmentId: number, page: number) {
    return this.itemBlockRepository.find({
      where: {
        assessment: { id: assessmentId },
        section: page,
      },
      relations: ['questions', 'options'],
    });
  }

  findOne(assessmentId: number) {
    return this.itemBlockRepository.findOne({
      where: {
        assessment: { id: assessmentId },
      },
      relations: ['questions', 'options'],
    });
  }

  async updateOne(id: number, updateItemBlockDto: UpdateItemBlockDto) {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    const oldType = item.type as ItemBlockType;
    const newType = updateItemBlockDto.type as ItemBlockType;

    // Update the main item properties
    const updatedItem = Object.assign(item, updateItemBlockDto);
    await this.itemBlockRepository.save(updatedItem);

    // Handle type transitions
    await this.handleTypeTransition(id, oldType, newType, item);

    // Return complete updated item
    return this.loadCompleteItemBlock(id);
  }

  private async handleTypeTransition(
    itemBlockId: number,
    oldType: ItemBlockType,
    newType: ItemBlockType,
    existingItem: ItemBlock,
  ): Promise<void> {
    // If types are the same, no transition needed
    if (oldType === newType) {
      return;
    }

    // Clean up old type-specific entities
    await this.cleanupOldTypeEntities(
      itemBlockId,
      oldType,
      newType,
      existingItem,
    );

    // Create new type-specific entities
    await this.createNewTypeEntities(itemBlockId, oldType, newType);
  }

  private async cleanupOldTypeEntities(
    itemBlockId: number,
    oldType: ItemBlockType,
    newType: ItemBlockType,
    existingItem: ItemBlock,
  ): Promise<void> {
    // Remove options if new type doesn't need them
    if (this.needsOptions(oldType) && !this.needsOptions(newType)) {
      await this.optionsService.removeByItemBlockId(itemBlockId);
    }

    // Handle GRID specific cleanup (remove non-header questions)
    if (oldType === ItemBlockType.GRID && newType !== ItemBlockType.GRID) {
      for (const question of existingItem.questions || []) {
        if (!question.isHeader) {
          await this.questionService.remove(question.id);
        }
      }
    }

    // Remove all questions if new type doesn't need them
    if (
      this.isQuestionBasedType(oldType) &&
      !this.isQuestionBasedType(newType)
    ) {
      await this.questionService.removeByItemBlockId(itemBlockId);
    }

    // Remove header/image bodies when transitioning away
    if (oldType === ItemBlockType.HEADER && newType !== ItemBlockType.HEADER) {
      await this.headerBodyRepository.delete({ itemBlockId });
    }
    if (oldType === ItemBlockType.IMAGE && newType !== ItemBlockType.IMAGE) {
      await this.imageBodyRepository.delete({ itemBlockId });
    }
  }

  private async createNewTypeEntities(
    itemBlockId: number,
    oldType: ItemBlockType,
    newType: ItemBlockType,
  ): Promise<void> {
    // Create questions if transitioning to question-based type
    if (
      !this.isQuestionBasedType(oldType) &&
      this.isQuestionBasedType(newType)
    ) {
      await this.createQuestionsForBlock(itemBlockId, newType);
    }
    // Add additional GRID header question if transitioning from question-based type to GRID
    else if (
      this.isQuestionBasedType(oldType) &&
      oldType !== ItemBlockType.GRID &&
      newType === ItemBlockType.GRID
    ) {
      await this.questionService.create({
        itemBlockId,
        isHeader: false,
      });
    }

    // Create options if transitioning to option-based type
    if (!this.needsOptions(oldType) && this.needsOptions(newType)) {
      await this.createDefaultOption(itemBlockId);
    }

    // Create header body if transitioning to HEADER
    if (oldType !== ItemBlockType.HEADER && newType === ItemBlockType.HEADER) {
      await this.createHeaderBody(itemBlockId);
    }

    // Create image body if transitioning to IMAGE
    if (oldType !== ItemBlockType.IMAGE && newType === ItemBlockType.IMAGE) {
      await this.createImageBody(itemBlockId);
    }
  }

  async removeOne(id: number): Promise<void> {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    // ลบข้อมูลสัมพันธ์
    if (item.questions?.length) {
      await this.questionService.removeByItemBlockId(id);
    }

    if (item.options?.length) {
      await this.optionsService.removeByItemBlockId(id);
    }

    if (item.headerBody) {
      await this.headerBodyRepository.delete({ itemBlockId: id });
    }

    if (item.imageBody) {
      await this.imageBodyRepository.delete({ itemBlockId: id });
    }

    // ลบ itemBlock หลัก
    await this.itemBlockRepository.delete(id);
  }
  // item-blocks.service.ts

  async removeAllByAssessmentId(assessmentId: number): Promise<void> {
    const itemBlocks = await this.itemBlockRepository.find({
      where: { assessment: { id: assessmentId } },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    for (const block of itemBlocks) {
      await this.itemBlockRepository.remove(block);
    }
  }

  async sequenceQuestion(submissionId: number, sequence: number) {
    // avoid header sequence 1
    sequence = Number(sequence) + 1;

    // Get submission and question block in parallel
    const [submission, submissionResponses] = await Promise.all([
      // use EntityManager instead of Repository
      this.entityManager.findOne(Submission, {
        where: { id: submissionId },
      }),
      // use Repository instead of EntityManager
      await this.entityManager.findOne(Submission, {
        where: { id: submissionId },
        relations: {
          responses: {
            selectedOption: {
              itemBlock: true,
            },
          },
        },
        select: {
          id: true, // 🟡 ต้องระบุ id ถ้าจะใช้ select
          responses: {
            id: true, // ✅ ต้อง select responses ด้วย
            selectedOptionId: true,
            selectedOption: {
              id: true,
              optionText: true,
              itemBlockId: true,
              itemBlock: {
                id: true,
                sequence: true,
              },
            },
          },
        },
      }),
    ]);

    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${submissionId} not found`,
      );
    }

    // validate submission is not closed
    if (submission.endAt < new Date()) {
      throw new ForbiddenException('Submission is closed');
    }

    const questionBlock = await this.itemBlockRepository.findOne({
      where: {
        sequence,
        assessmentId: submission.assessmentId,
      },
      relations: {
        questions: true,
        options: true,
      },
    });

    if (!questionBlock) {
      throw new NotFoundException(
        `Question block with sequence ${sequence} not found`,
      );
    }

    const totalQuestions = await this.itemBlockRepository.count({
      where: { assessmentId: submission.assessmentId },
    });

    // skip header sequence 1
    const questionList = new Array(totalQuestions - 1)
      .fill(null)
      .map((_, i) => ({
        sequence: i + 2,
        isDone: false,
      }));

    // Mark completed questions based on responses
    if (submissionResponses?.responses) {
      const responseMap = new Set(
        submissionResponses.responses.map(
          (response) => response.selectedOption.itemBlock.sequence,
        ),
      );

      questionList.forEach((question) => {
        question.isDone = responseMap.has(question.sequence);
      });
    }

    // if user have response and response from itemblock with many question
    if (questionBlock && questionBlock.options.length > 0) {
      questionBlock.options?.forEach((option) => {
        delete option.value;
      });
    }

    console.log('responses', submissionResponses?.responses);

    // return response for that question block

    const response = submissionResponses.responses.filter(
      (response) => response.selectedOption.itemBlock.sequence === sequence,
    );

    return {
      questionBlock,
      response: response || null,
      isLast: sequence === totalQuestions,
      questionList,
    };
  }

  async findItemOne(itemBlockId: number) {
    return this.itemBlockRepository.findOne({
      where: {
        id: itemBlockId,
      },
      relations: ['questions', 'options'],
    });
  }
}
