import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAssessmentDto } from '../creates/create-assessment.dto';
import {
  IsBoolean,
  IsDate,
  isNumber,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateAssessmentDto extends PartialType(CreateAssessmentDto) {
  @IsString()
  @IsOptional()
  name?: string;

  @IsNumber()
  @IsOptional()
  submitLimit?: number;

  @IsNumber()
  @IsOptional()
  timeout?: number;

  @IsDate()
  @IsOptional()
  startAt: Date;

  @IsDate()
  @IsOptional()
  endAt: Date;

  @IsOptional()
  @IsBoolean()
  @Transform(
    ({ value }) => {
      if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
      }
      return Boolean(value); // Handles true/false/null/undefined
    },
    { toClassOnly: true },
  )
  status?: boolean;

  @IsNumber()
  @IsOptional()
  totalScore?: number;

  @IsNumber()
  @IsOptional()
  passRatio?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(
    ({ value }) => {
      if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
      }
      return Boolean(value);
    },
    { toClassOnly: true },
  )
  isPrototype?: boolean;

  @IsBoolean()
  @IsOptional()
  responseEdit?: boolean;
}
