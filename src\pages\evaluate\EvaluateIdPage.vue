<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserTextBlock
          v-if="evaluateItem.headerBody && evaluateItem.type === 'HEADER'"
          :item="evaluateItem"
          :title="evaluateItem.headerBody.title || 'ไม่มีข้อมูล'"
          :description="evaluateItem.headerBody.description || 'ไม่มีข้อมูล'"
        />
      </div>

      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserQuestionBlock
          v-if="evaluateItem.type !== 'IMAGE'"
          :id="evaluateItem.id"
          :draftId="submission?.id || 0"
          :item="editFormData"
          :category="evaluateItem.type"
          :section="evaluateItem.section"
          :status="isPreview"
          @update-answer="handleAnswer"
        />
        <UserImageBlock
          v-if="evaluateItem.type === 'IMAGE'"
          :title="evaluateItem.imageBody?.imageText || ''"
          :image-url="evaluateItem.imageBody?.imagePath || ''"
          @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
          @update:image-url="evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)"
          aria-label="dsda"
        />
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <q-btn v-if="isPreview" label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <q-btn v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <q-btn v-show="hideButton" label="หน้าต่อไป" @click="nextSection" />
      <div class="col-auto">
        <q-btn v-if="!hideButton && isPreview" label="ส่งแบบสอบถาม" @click="submitForm" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import { onMounted, ref, watch, watchEffect } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { summaryService } from 'src/services/asm/submissionService'; // หรือ path ที่คุณวางไฟล์จริงๆ
import type { Assessment, Submission, User } from 'src/types/models';
import { useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import router from 'src/router';
import { onBeforeRouteUpdate } from 'vue-router';
onMounted(async () => {
  const sectionParam = Number(route.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }

  if (route.meta.status === 'do') {
    const url = String(route.params.url);
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(url);

    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };

    if (route.name === 'evaluate-do' && user.value) {
      console.log(Number(route.query.id));
      const sub = await summaryService.getDraft(Number(route.query.id), user.value?.id);
      submission.value = sub;
      console.log(submission.value);

      if (!submission.value) {
        await summaryService.create({
          id: 0,
          assessmentId: Number(route.query.id),
          userId: user.value?.id,
          startAt: '',
          endAt: '',
        });
      } else {
        console.log('draft');
      }
    }
  } else {
    const res = await new AssessmentService('evaluate').fetchOne(Number(route.params.id));
    editFormData.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };
  }
});

onBeforeRouteUpdate(async (to) => {
  const sectionParam = Number(to.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }

  if (to.meta.status === 'do') {
    const url = String(to.params.url);
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(url);

    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };
    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...res.data,
      itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  } else if (to.meta.status === 'preview') {
    const res = await new AssessmentService('evaluate').fetchOne(Number(to.params.id));

    editFormData.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };

    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  }
});

const route = useRoute();
const checkItem = ref<Assessment>();
const editFormData = ref<Assessment>();
const isPreview = ref(true);
const evaluateId = ref(0);
const currentSection = ref(1);
const previous = ref(false);
//Submittion
const submission = ref<Submission>();
const user = ref<User>();
const authStore = useAuthStore();

watch(
  () => route.params,
  (params) => {
    evaluateId.value = Number(params.id);
    if (String(route.params.status) === 'preview') {
      isPreview.value = false;
    } else if (String(route.params.status) === 'do') {
      isPreview.value = true;
    }
  },
  { immediate: true },
);

watch(
  () => route.meta,
  (meta) => {
    if (meta.status === 'preview') {
      isPreview.value = false;
    } else if (meta.status === 'do') {
      isPreview.value = true;
    }
  },
  { immediate: true },
);

// watch(
//   () => route, // watch ตัว route ทั้ง object
//   async (route) => {
//     if (route.meta.status === 'do') {
//       const url = String(route.params.url);
//       currentSection.value = Number(route.params.section);
//       user.value = authStore.getCurrentUser();

//       const res = await new AssessmentService('evaluate').getAssessmentByUUID(url);

//       // เซ็ตค่าที่ถูก filter ให้ editFormData หลังโหลดข้อมูลเสร็จ
//       editFormData.value = {
//         ...res.data,
//         itemBlocks:
//           res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
//       };

//       // เช็คว่าอยู่ในหน้า do หรือไม่
//       if (route.name === 'evaluate-do' && user.value) {
//         const sub = await summaryService.getDraft(Number(editFormData.value?.id), user.value?.id);
//         submission.value = sub;

//         if (submission.value) {
//           console.log('draft');
//         } else {
//           await summaryService.create({
//             id: 0,
//             assessmentId: Number(route.params.id),
//             userId: user.value?.id,
//             startAt: '',
//             endAt: '',
//           });
//         }
//       }
//     } else if (route.meta.status === 'preview') {
//       const res = await new AssessmentService('evaluate').fetchOne(Number(route.params.id));

//       // เซ็ตค่าที่ถูก filter ให้ editFormData หลังโหลดข้อมูลเสร็จ
//       editFormData.value = {
//         ...res,
//         itemBlocks: res.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
//       };
//       console.log(editFormData.value);
//     }
//   },
//   { immediate: true },
// );

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number>;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
});

const hideButton = ref(true);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  if (isPreview.value === false) {
    await router.push({
      name: 'evaluate-preview',
      params: {
        id: Number(route.params.id),
        section: currentSection.value,
      },
    });
  } else {
    await router.push({
      name: 'evaluate-do',
      params: {
        url: String(route.params.url),
        section: currentSection.value,
      },
    });
  }
};

const nextSection = async () => {
  await updateSection(currentSection.value + 1);
};

const previousSection = async () => {
  if (currentSection.value > 1) {
    currentSection.value--;
    previous.value = true;
    await updateSection(currentSection.value);
  }
};
const STORAGE_KEY = 'draft-form';
const clearForm = () => {
  localStorage.removeItem(STORAGE_KEY); // ลบค่าที่เก็บไว้
};

const submitForm = async () => {
  if (user.value && submission.value) {
    await summaryService.update(submission.value.id, {
      id: submission.value.id, // เผื่อจำเป็น
      assessmentId: submission.value.assessmentId,
      userId: user.value.id,
      startAt: submission.value.startAt,
      endAt: new Date().toISOString(),
    });
  }
};
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>
