import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { Submission } from './submission.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';

@Entity('responses')
export class Response {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  submissionId: number;

  @Column({ nullable: true })
  selectedOptionId: number | null;

  @Column()
  questionId: number;

  @ManyToOne(() => Submission, (submission) => submission.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'submissionId' })
  submission: Submission;

  @ManyToOne(() => Option, (option) => option.responses, { nullable: true })
  @JoinColumn({ name: 'selectedOptionId' })
  selectedOption: Option;

  @ManyToOne(() => Question, (question) => question.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'questionId' })
  question: Question;
}
