import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Option } from 'src/types/models';

// Define types for option creation and update
export interface CreateOptionData {
  optionText: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export interface UpdateOptionData {
  optionText?: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export class OptionService {
  private path = '/options';

  /**
   * Create option using POST /options with itemBlockId in request body
   * POST /options
   */
  async createOption(data: CreateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.post<Option>(this.path, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'สร้างตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error creating option:', error);
      Notify.create({
        type: 'negative',
        message: 'สร้างตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Create option failed');
    }
  }

  /**
   * Create option for a specific itemBlock (wrapper method)
   * POST /options
   */
  async createOptionForItemBlock(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    // Ensure itemBlockId is included in the request body
    const requestData = {
      ...data,
      itemBlockId: itemBlockId,
    };

    return this.createOption(requestData, file);
  }

  /**
   * Create option for a specific question
   * POST /options/{questionId}
   */
  async createOptionForQuestion(
    questionId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }
      console.log(data);

      const formData = this.toFormData(data, file);
      for (const [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      const response = await api.post<Option>(`${this.path}/${questionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'สร้างตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error creating option for question:', error);
      Notify.create({
        type: 'negative',
        message: 'สร้างตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Create option failed');
    }
  }

  createOptionForQuestionFormData(questionId: number, formData: FormData): Promise<Option> {
    return api.post(`/options/${questionId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Update option by optionId
   * PATCH /options/{optionId}
   */
  async updateOption(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'อัปเดตตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error updating option:', error);
      Notify.create({
        type: 'negative',
        message: 'อัปเดตตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Update option failed');
    }
  }

  /**
   * Update option for a specific question
   * PATCH /options/{questionId}/{optionId}
   */
  async updateOptionForQuestion(
    questionId: number,
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${questionId}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'อัปเดตตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error updating option for question:', error);
      Notify.create({
        type: 'negative',
        message: 'อัปเดตตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Update option failed');
    }
  }

  // Keep existing methods for backward compatibility
  async getAllOptions(): Promise<Option[]> {
    try {
      const response = await api.get<Option[]>(this.path);
      return response.data;
    } catch (error) {
      console.error('Error fetching all options:', error);
      throw new Error('Fetch options failed');
    }
  }

  async getOptionById(id: number): Promise<Option> {
    try {
      const response = await api.get<Option>(`${this.path}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching option by ID:', error);
      throw new Error('Fetch option failed');
    }
  }

  async removeOption(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({
        type: 'positive',
        message: 'ลบตัวเลือกสำเร็จ',
        position: 'top',
      });
    } catch (error) {
      console.error('Error removing option:', error);
      Notify.create({
        type: 'negative',
        message: 'ลบตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Remove option failed');
    }
  }

  /**
   * Helper method to convert data to FormData for multipart/form-data requests
   */
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value !== null && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          formData.append(key, String(value));
        } else {
          formData.append(key, JSON.stringify(value));
        }
      }
    });

    if (file) {
      formData.append('file', file);
    }

    return formData;
  }

  /**
   * Validate option data before sending to API
   */
  private validateOptionData(data: CreateOptionData | UpdateOptionData): void {
    if (!data.itemBlockId) {
      throw new Error('itemBlockId is required');
    }

    if (
      'optionText' in data &&
      data.optionText !== undefined &&
      typeof data.optionText !== 'string'
    ) {
      throw new Error('optionText must be a string');
    }

    if (data.value !== undefined && typeof data.value !== 'number') {
      throw new Error('value must be a number');
    }
  }

  /**
   * Create option with validation
   */
  async createOptionWithValidation(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.createOptionForItemBlock(itemBlockId, data, file);
  }

  /**
   * Update option with validation
   */
  async updateOptionWithValidation(
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.updateOption(optionId, data, file);
  }

  /**
   * Update option without notifications (for auto-save)
   * PATCH /options/{optionId}
   */
  async updateOptionSilent(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      // No notification for silent updates (auto-save)
      return response.data;
    } catch (error) {
      console.error('Error updating option (silent):', error);
      throw new Error('Update option failed');
    }
  }
}
