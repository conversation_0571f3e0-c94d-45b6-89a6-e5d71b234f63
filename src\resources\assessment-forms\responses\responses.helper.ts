import { Entity<PERSON>anager } from 'typeorm';
import { Response } from '../entities/response.entity';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { Submission } from 'src/resources/assessment-forms/entities/submission.entity';
import { Option } from 'src/resources/assessment-forms/entities/option.entity';
import { Question } from 'src/resources/assessment-forms/entities/question.entity';
import { InjectEntityManager } from '@nestjs/typeorm';

export class ResponsesHelper {
  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  // 🔷 Validate submission
  async validateSubmission(submissionId: number): Promise<Submission> {
    console.log('validateSubmission', submissionId);
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
    });

    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${submissionId} not found`,
      );
    }
    if (submission.endAt < new Date()) {
      throw new ForbiddenException('Submission is closed');
    }
    if (submission.submitAt) {
      throw new ForbiddenException('Submission already submitted');
    }

    return submission;
  }

  // 🔷 Save or update Option สำหรับ textAnswer
  async saveOrUpdateOptionForTextAnswer(
    answerText: string,
    selectedOptionId: number | null,
    questionId: number,
  ): Promise<number> {
    const question = await this.entityManager.findOne(Question, {
      where: { id: questionId },
    });
    if (!question) {
      throw new NotFoundException(`Question with ID ${questionId} not found`);
    }

    let option: Option | null = null;
    if (selectedOptionId) {
      option = await this.entityManager.findOne(Option, {
        where: { id: selectedOptionId },
      });
    }

    if (option) {
      // Update existing option
      option.optionText = answerText;
    } else {
      // Create new option
      option = this.entityManager.create(Option, {
        itemBlockId: question.itemBlockId,
        optionText: answerText,
        sequence: 1,
        value: 0,
        nextSection: null,
      });
    }

    const savedOption = await this.entityManager.save(Option, option);
    return savedOption.id;
  }

  // 🔷 Save or update Response
  async saveOrUpdateResponse(
    submissionId: number,
    questionId: number,
    selectedOptionId: number | null,
    responseId: number | null,
  ): Promise<Response> {
    if (responseId) {
      const existingResponse = await this.entityManager.findOne(Response, {
        where: { id: responseId },
      });
      if (!existingResponse) {
        throw new NotFoundException(`Response with ID ${responseId} not found`);
      }
      existingResponse.selectedOptionId = selectedOptionId;
      return this.entityManager.save(Response, existingResponse);
    } else {
      const newResponse = this.entityManager.create(Response, {
        submissionId,
        questionId,
        selectedOptionId,
      });
      return this.entityManager.save(Response, newResponse);
    }
  }
}
